﻿/********* LOAD PLAN SALLE *********
declare @plangCode varchar = 'fr'
declare @psessionid int = 11


declare @pFirstzoneid int = 0
declare @pFirstfoorId int = 0
declare @pFirstsectionId int = 0
*/

if (@pFirstzoneid >0)

	select zone_id into #myZones FROM zone WHERE zone_id in ({listzonesId})

if (@pFirstfoorId >0)
begin
	select etage_id into #myFloors FROM etage WHERE etage_id in ({listfloorsId})
end

if (@pFirstsectionId >0)
begin
	select section_id into #mySections FROM section WHERE section_id in ({listsectionsId})
end


declare @SQL varchar(max)
set @SQL = 'SELECT  seance_id, e.entree_id,
rlp.siege as seat, rlp.rang as row, rlp.orientation, rlp.type_siege, pos_x, pos_y,
decal_x, decal_y, orientation, 
z.zone_id, et.etage_id,sect.section_id,d.denom_nom,
cat.categ_id, 
z.zone_nom, et.etage_nom, sect.section_nom , cat.categ_nom, res.reserve_nom, alot.alot_nom, co.conting_nom,  pptrib.nom as tribune, ppacces.nom as acces, ppporte.nom as gate, e.lieu_configuration_id as lieu_id,

''N'' as IsFree, '
   

set @SQL += ' ''N''  as IsMine '

set @SQL += ',rlp.iindex
,e.reserve_id
,rlp.denomination_id, rlp.bordure, e.entree_etat
FROM entree_[eventID] e
INNER JOIN reference_lieu_physique rlp ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id
INNER JOIN denomination d on d.denom_id = rlp.denomination_id
INNER JOIN zone z ON z.zone_id = rlp.zone_id 
INNER JOIN etage et ON et.etage_id = rlp.etage_id 
INNER JOIN section sect ON sect.section_id = rlp.section_id
INNER JOIN categorie cat ON cat.categ_id=e.categorie_id

left outer JOIN reserve res ON res.reserve_id =e.reserve_id
left outer JOIN alotissement alot ON alot.alot_id = e.alotissement_id
left outer JOIN contingent co ON co.conting_id = e.contingent_id
left outer JOIN Propriete_physique pptrib ON pptrib.id = rlp.tribune 
left outer JOIN Propriete_physique ppacces ON ppacces.id = rlp.acces 
left outer JOIN Propriete_physique ppporte ON ppporte.id = rlp.porte 

 WHERE entree_etat in (''X'',''I'')
  AND e.seance_id=' + CONVERT(varchar(50), @pSessionId) 

  
if @pFirstzoneid > 0
begin
	set @SQL += ' AND z.zone_id in (select zone_id from #myZones)'
end

 if @pFirstfoorId > 0
begin
	set @SQL += ' AND et.etage_id in (select etage_id from #myFloors)'
end

 if @pFirstsectionId > 0
begin
	set @SQL += ' AND sect.section_id in (select section_id from #mySections)'
end

print @sql
EXEC(@SQL)

	  -- AND z.zone_id=0  AND et.etage_id=0
	   -- AND sect.section_id=0  


