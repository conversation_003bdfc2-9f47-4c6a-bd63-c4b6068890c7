﻿--DECLARE @pIdentiteId INT = 2

DECLARE @SqlForDossier VARCHAR(4000), @SqlForDossierSvg VARCHAR(4000)
DECLARE @dossierId INT, @manifId INT

/* Définission des infos de commandes pour le fetch*/
DECLARE CommandeLigneInfos CURSOR SCROLL FOR 

	SELECT cl.dossier_id, cl.manifestation_id
	FROM commande c 
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	WHERE  cl.type_ligne ='DOS' 
	AND (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId)

OPEN CommandeLigneInfos; 
FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId;

/* Création et remplissage de la table #Dossier*/
CREATE TABLE #Dossier (
	manifestation_id_dossier INT,
	dossier_id INT,
	identite_id INT, 
	commande_id INT,
	abon_manif_id INT,
	seance_id INT, 
	dossier_v INT,
	operateur_id INT,
	dossier_montant DECIMAL(18,10),
	dossier_montant1 DECIMAL(18,10),
	dossier_montant2 DECIMAL(18,10),
	dossier_montant3 DECIMAL(18,10),
	dossier_montant4 DECIMAL(18,10),
	dossier_montant5 DECIMAL(18,10),
	dossier_montant6 DECIMAL(18,10),
	dossier_montant7 DECIMAL(18,10),
	dossier_montant8 DECIMAL(18,10),
	dossier_montant9 DECIMAL(18,10),
	dossier_montant10 DECIMAL(18,10),
	dossier_c CHAR(50),
	dossier_etat CHAR(1),
	dossier_icone INT,
	dossier_numero INT,
	dossier_nbplace INT,
	dossier_montantpayer DECIMAL(18,10),
	dossier_facture INT,
	dossier_client_nom CHAR(40),
	num_paiement INT,
	date_operation DATETIME,
	operation_id INT,
	filiere_id INT
)

/* Création et remplissage de la table #DossierSvg*/
CREATE TABLE #DossierSvg (
	manifestation_id_dossier_svg INT,
	dossier_id INT,
	identite_id INT, 
	commande_id INT,
	abon_manif_id INT,
	seance_id INT, 
	dossier_v INT,
	operateur_id INT,
	dossier_montant DECIMAL(18,10),
	dossier_montant1 DECIMAL(18,10),
	dossier_montant2 DECIMAL(18,10),
	dossier_montant3 DECIMAL(18,10),
	dossier_montant4 DECIMAL(18,10),
	dossier_montant5 DECIMAL(18,10),
	dossier_montant6 DECIMAL(18,10),
	dossier_montant7 DECIMAL(18,10),
	dossier_montant8 DECIMAL(18,10),
	dossier_montant9 DECIMAL(18,10),
	dossier_montant10 DECIMAL(18,10),
	dossier_c CHAR(50),
	dossier_etat CHAR(1),
	dossier_icone INT,
	dossier_numero INT,
	dossier_nbplace INT,
	dossier_montantpayer DECIMAL(18,10),
	dossier_facture INT,
	dossier_client_nom CHAR(40),
	num_paiement INT,
	date_operation DATETIME,
	type_operation CHAR(10),
	filiere_id INT
)

CREATE TABLE #Commande_buy_date (
	commande_id INT,
	buy_date DATETIME,
)

/* Remplissage des tables temporaire*/
WHILE @@FETCH_STATUS=0 
BEGIN 
	SET @SqlForDossier = 
	'INSERT INTO #Dossier 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_dossier, d.*
		FROM dossier_' + LTRIM(STR(@manifId)) + ' d
		WHERE d.dossier_id = ' + LTRIM(STR(@dossierId))

	SET @SqlForDossierSvg = 
	'INSERT INTO #DossierSvg 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_dossier_svg, dsvg.*
		FROM dossiersvg_' + LTRIM(STR(@manifId)) + ' dsvg
		WHERE dsvg.dossier_id = ' + LTRIM(STR(@dossierId))

	PRINT(@SqlForDossier)
	PRINT(@SqlForDossierSvg)

	EXEC(@SqlForDossier)
	EXEC(@SqlForDossierSvg)

FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId; 

END

CLOSE CommandeLigneInfos; 
DEALLOCATE CommandeLigneInfos;

INSERT INTO #Commande_buy_date 
SELECT date_info.commande_id, MIN(date_info.buy_date) 
FROM (
	SELECT d.date_operation as buy_date, c.commande_id as commande_id
	FROM commande c 
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	INNER JOIN #DossierSvg d ON c.commande_id = d.commande_id
	WHERE (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId)
	AND (d.dossier_etat = 'P' OR d.dossier_etat = 'B')
	UNION
	SELECT dp.date_operation as buy_date, c.commande_id as commande_id
	FROM commande c 
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	INNER JOIN dossier_produit dp ON cl.commande_id = dp.commande_id
	WHERE (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId)
	AND (dp.dos_prod_etat = 'P' OR dp.dos_prod_etat = 'B')
) as date_info
GROUP BY date_info.commande_id

/*Fin remplissage des tables temporaire*/

SELECT DISTINCT c.*, bd.buy_date
FROM commande c 
INNER JOIN #Commande_buy_date bd ON c.commande_id = bd.commande_id
INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
WHERE (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId)
--1) Les produits et dossiers ne doivent pas tous être annulés et contenir un produit résa
AND 'P' = ANY (SELECT dsvg.dossier_etat COLLATE French_CI_AS AS etat
				FROM #DossierSvg dsvg
				WHERE dsvg.commande_id = c.commande_id
				UNION
				SELECT dp.dos_prod_etat COLLATE French_CI_AS AS etat
				FROM dossier_produit dp
				INNER JOIN produit p ON dp.produit_id = p.produit_id
				WHERE dp.commande_id = c.commande_id
				AND p.groupe_id != 12)
--2) Les produits et dossiers ne doivent contenir aucun etat Reservé
AND 'R' != ALL (SELECT d.dossier_etat COLLATE French_CI_AS AS etat
			    FROM #Dossier d 
			    WHERE d.commande_id = c.commande_id
			    UNION
			    SELECT dp.dos_prod_etat COLLATE French_CI_AS  AS etat
			    FROM dossier_produit dp
			    INNER JOIN produit p ON dp.produit_id = p.produit_id
			    WHERE dp.commande_id = c.commande_id)
ORDER BY c.commande_id DESC

/*Suppression des tables temporaires*/
DROP TABLE #Dossier
DROP TABLE #DossierSvg
DROP TABLE #Commande_buy_date




