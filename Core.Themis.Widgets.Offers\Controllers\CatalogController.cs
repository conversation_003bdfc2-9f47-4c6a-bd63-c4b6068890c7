﻿using AutoMapper;
using Core.Themis.Libraries.BLL.EventsSessions.Interfaces;
using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Catalog.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Catalog.Filter;
using Core.Themis.Libraries.Razor.Areas.Catalog.ViewModels;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Widgets;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Http.Extensions;
using System;
using System.Collections.Generic;
using System.Web;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class CatalogController : Controller
    {
        private static readonly RodrigueNLogger Logger = new();

        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IConfiguration _configuration;
        private readonly IPartnerManager _partnerManager;
        private readonly IEventGenreManager _eventGenreManager;
        private readonly IAbonnementManager _abonnementManager;
        private readonly IEventManager _eventManager;
        private readonly IMemoryCache _memoryCache;
        private readonly IMapper _mapper;
        private readonly ITranslateManager _translateManager;
        private readonly ICatalogManager _eventsCatalogManager;

        public CatalogController(
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            IConfiguration configuration,
            IPartnerManager partnerManager,
            IEventGenreManager eventGenreManager,
            IAbonnementManager abonnementManager,
            IEventManager eventManager,
            IMemoryCache memoryCache,
            IMapper mapper,
            ITranslateManager translateManager,
            ICatalogManager eventsCatalogManager
            )
        {
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _configuration = configuration;
            _partnerManager = partnerManager;
            _eventGenreManager = eventGenreManager;
            _eventManager = eventManager;
            _abonnementManager = abonnementManager;
            _memoryCache = memoryCache;
            _mapper = mapper;
            _translateManager = translateManager;
            _eventsCatalogManager = eventsCatalogManager;
        }


        #region Views

        [HttpPost]
        [Route("Catalog")]
        public IActionResult Catalog(int structureId, string langCode, int identityId, int buyerProfilId, string partnerName, 
            string htmlSelector, string identiteHash ="notset", 
            int? catalogId = null, int? eventGroupId = null, int? genreId = null, int? subGenreId = null, int? targetId = null, string? search = null 
            ,string? linkToEvent = null)
        {
            try
            {
                Logger.Info(structureId, $"Catalog{langCode} {partnerName} {identiteHash} {htmlSelector}");

                PartnerDTO? partner = _partnerManager.GetPartnerInfosByName( partnerName);

                if (partner == null)
                    return BadRequest("Partner doesn't exist");

                string? widgetSignature = HttpContext.Request.Headers["Signature"];

                if (widgetSignature == null)
                    return BadRequest("signature is missing");

                string partnerToken = WidgetSecurityHelper.GeneratePartnerToken(structureId, partner.PartnerName, partner.SecretKey);

                List<string> lstParamsToHash =
                [
                    structureId.ToString(),
                    langCode,
                    identityId.ToString(),
                    buyerProfilId.ToString(),
                    catalogId.ToString(), 
                    eventGroupId.ToString(), 
                    genreId.ToString(), 
                    subGenreId.ToString(), 
                    targetId.ToString(), 
                    search,
                    linkToEvent,
                    partnerName,
                    partnerToken,
                    widgetSignature
                ];
                if (identiteHash == null)
                {
                    identiteHash = "nset";
                }

                string hash = WidgetSecurityHelper.GenerateHash(partner.SecretKey, lstParamsToHash);
                string queryHash = WidgetSecurityHelper.GenerateQueryHash(widgetSignature, partnerToken, partnerName);

                string action = RouteData.Values["action"].ToString();
                string callUrl = $"{action}Hash/{structureId}/{langCode}/{identityId}/{identiteHash}/{buyerProfilId}/?htmlSelector={htmlSelector}&hash={hash}&queryHash={queryHash}&domainReferrerUrl={RodrigueHttpContext.DomainReferrerUrl}";

                if (catalogId.HasValue)
                    callUrl += $"&catalogId={catalogId}";

                if (eventGroupId.HasValue)
                    callUrl += $"&eventGroupId={eventGroupId}";

                if (genreId.HasValue)
                    callUrl += $"&genreId={genreId}";

                if (subGenreId.HasValue)
                    callUrl += $"&subGenreId={subGenreId}";

                if (targetId.HasValue)
                    callUrl += $"&targetId={targetId}";

                if (!string.IsNullOrEmpty(search))
                    callUrl += $"&search={search}";

                if (!string.IsNullOrEmpty(linkToEvent))
                    linkToEvent = HttpUtility.UrlEncode(linkToEvent);
                    callUrl += $"&linkToEvent={linkToEvent}";


                Console.WriteLine(Json(new { responseText = callUrl }));

                return Json(new { responseText = callUrl });
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"{ex.Message} \n {ex.InnerException} \n {ex.StackTrace}");
                throw;
            }
        }

        [Route("CatalogHash/{structureId}/{langCode}/{identityId}/{identiteHash}/{buyerProfilId}")]
        public IActionResult EventsCatalogHash(int structureId, string langCode,  string htmlSelector, string hash, string queryHash, 
            int identityId, string identiteHash, int buyerProfilId, 
            string domainReferrerUrl, int? catalogId = null, 
            int? eventGroupId = null, int? genreId = null, int? subGenreId = null, int? targetId = null, string? search = null, string? linkToEvent = null )
        {
            try
            {
                SecurityInfos securityData = WidgetSecurityHelper.GetSecurityInfos(queryHash);
                PartnerDTO? partnerInDB = _partnerManager.GetPartnerInfosByName(securityData.PartnerName);

                if (partnerInDB is null)
                    return BadRequest(new { message = "Partner doesn't exist" });

                Logger.Info(structureId, $"CatalogHash {langCode} {securityData.PartnerName} {htmlSelector}, {domainReferrerUrl}");

                List<string> lstParamsToHash =
                [
                    structureId.ToString(),
                    langCode,
                    identityId.ToString(),
                    buyerProfilId.ToString(),
                    catalogId.ToString(),
                    eventGroupId.ToString(),
                    genreId.ToString(),
                    subGenreId.ToString(),
                    targetId.ToString(),
                    search,
                    linkToEvent,
                    partnerInDB.PartnerName,
                    securityData.RequestPartnerToken,
                    securityData.RequestWidgetSignature
                ]; 

               if (!WidgetSecurityHelper.CheckAccessAuthorized(hash, queryHash, lstParamsToHash, partnerInDB.SecretKey))
                    return Unauthorized();

                string identiteSalt = _configuration["identiteSalt"]!;

                if (identiteSalt != "none")
                {
                    if (!identiteHash.CheckHash(structureId, identityId, identiteSalt))
                    {
                        Logger.Error(structureId, $"CatalogHash {identityId}, {identiteHash} do not match calculated hash !");
                        return Unauthorized();
                    }
                }

                var translateAreas = _configuration.GetSection("TranslationsAreas:EventsCatalog").Get<string[]>();

                var dico = _translateManager.GetDicoByLangCode(structureId, langCode, true, translateAreas);


                CatalogAppSettings appSettings = new()
                {
                    DomainReferrerUrl = domainReferrerUrl,
                    AppStartingRoute = "./catalog",
                    StructureId = structureId,
                    LangCode = langCode,
                    IdentiteId = identityId,
                    BuyerProfilId = buyerProfilId,
                    TranslationsList = dico,
                    HtmlSelector = htmlSelector,
                    CatalogId = catalogId,
                    LinkToEvent = HttpUtility.UrlDecode(linkToEvent),
                    WebUserId = 103440, 
                    FilterParams = new HomeModularFilterParams(eventGroupId, genreId, subGenreId, targetId, search)
                };
                ViewBag.SettingsMerge = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");
                return View("Index", appSettings);
            }
            catch (Exception ex)
            {
                Logger.Error(0, $"{ex.Message} \n {ex.InnerException} \n {ex.StackTrace}");

                return Problem($"CatalogHash {ex.Message} {ex.StackTrace}");
            }
        }

        #endregion
    }
}
