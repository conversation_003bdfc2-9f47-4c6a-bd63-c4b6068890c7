﻿--declare @identityId int
--set @identityId = 18


Declare @Manif_id int
Declare @Commande_id int

Declare @SQL varchar(max)
Declare @Tmptable as table (Commande_id int , Date_resa date,Nb_resa int, Mnt_resa int,Mnt_a_payer int,Manif_id int, seance_id int ,dossier_id int)



DECLARE cursor_reservations CURSOR
FOR

select distinct  cl.manifestation_id,commande_id  
from Commande_Ligne cl 
inner join seance s on s.seance_Id = cl.seance_id
where identite_id =@identityId and cl.manifestation_id > 0 
and s.seance_date_deb>DATEADD(YEAR,-1,GETDATE())

OPEN cursor_reservations

FETCH NEXT FROM cursor_reservations INTO @Manif_id,@Commande_id

WHILE @@FETCH_STATUS = 0
BEGIN

/* and dossier_nbplace =1  à la fin car pour l'instant on ne gere que les abo à coeff =1 */

set @SQL ='select distinct clc.commande_id as commande_id
		,convert( date,dsvg.date_operation) as Date_resa
		,dsvg.dossier_nbplace as Nb_résas
		,dsvg.dossier_montant * 100  as Mnt_résa
		,case when clc.Etat = ''R'' then dsvg.dossier_montant *100 else 0 end as Mnt_a_payer
		,clc.manifestation_id, clc.seance_id, clc.dossier_id
		FROM dossiersvg_'+ replace(STR(@Manif_id),' ','') +' dsvg
					inner join entreesvg_'+ replace(STR(@Manif_id),' ','') +' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v									
					inner join commande_ligne cmdl ON cmdl.commande_id=dsvg.commande_id AND cmdl.dossier_id=dsvg.dossier_id  and cmdl.seance_id=dsvg.seance_Id	
					inner join Commande_Ligne_comp clc on clc.commande_ligne_id = cmdl.commande_ligne_id		
					WHERE dsvg.identite_id = '+ replace(STR(@identityId),' ','') +' and 
					type_ligne=''DOS'' 
					 AND (dsvg.dossier_etat = ''R'' or dsvg.type_operation like  ''RESA%'') and dsvg.commande_id = '+ replace(STR(@Commande_id),' ','')

insert into @Tmptable exec (@SQL)

print @SQL

FETCH NEXT FROM cursor_reservations INTO @Manif_id,@Commande_id

END

CLOSE cursor_reservations
DEALLOCATE cursor_reservations


SELECT tt.commande_id,tt.date_resa,sum(tt.Nb_resa) as 'nb_article',sum(tt.Mnt_resa) as 'amount_reserved' ,sum(tt.Mnt_a_payer) 'amount_to_pay','DOS' as 'type',	
	case when SUM(tt.Mnt_resa - tt.Mnt_a_payer) = 0 then 0 else  1 end as isPartiallyPaid,
	(select date_limite from dossier_produit_reservation prodresa 
	inner join dossier_produit dp on dp.dos_prod_id = prodresa.dos_prod_id and dp.dos_prod_etat='P' and dp.commande_id = tt.Commande_id) 
	as date_limite 
 
 INTO #resultTable 
 from @Tmptable tt
 --where tt.Mnt_a_payer > 0
group by tt.commande_id,tt.date_resa 

 UNION
 
 SELECT   dpsvg.commande_id,convert(date,dpsvg.date_operation) as date_resa,sum(dpsvg.nb_produit) as 'nb_article',
  convert(int ,SUM(dpsvg.dos_prod_montant))* 100 as'amount_reserved' ,convert(int,SUM(dpsvg.dos_prod_montant - dp.dos_prod_montant))* 100 as 'amount_to_pay' ,'PRO' as 'type',
 case when SUM( (dpsvg.dos_prod_montant) - (dpsvg.dos_prod_montant - dp.dos_prod_montant) ) = 0 and sum(dpsvg.dos_prod_montant)>0 then 0 else  1 end as isPartiallyPaid,
 (select date_limite from dossier_produit_reservation prodresa 
	inner join dossier_produit dp on dp.dos_prod_id = prodresa.dos_prod_id and dp.dos_prod_etat='P' and dp.commande_id = dpsvg.Commande_id) as date_limite 
  from dossier_produitsvg dpsvg 
 inner join dossier_produit dp on dp.dos_prod_id = dpsvg.dos_prod_id and dp.dos_prod_v = dpsvg.dos_prod_v
 where dpsvg.identite_id = @identityId
and dpsvg.dos_prod_etat = 'R' 
 --and (dpsvg.dos_prod_montant - dp.dos_prod_montant) > 0
  group by dpsvg.commande_id ,convert(date,dpsvg.date_operation)  

  order by commande_id desc


   SELECT  SUM(nb_seats) as nb_seats, SUM(nb_products) as nb_products, commande_id, Date_resa, SUM(nb_article) as nb_article, SUM(amount_reserved) as amount_reserved, SUM(amount_to_pay) as amount_to_pay, '_' as type, 
   sum(isPartiallyPaid) as isPartiallyPaid, date_limite, SUM(formule_id) as formule_id, SUM(abo_id) as abo_id ,

	 case when date_limite>GETDATE() then 'ok' else 'no' end as date_limite_ok 
	 from #resultTable
	 GROUP BY Commande_id, Date_resa, date_limite
	 order by Commande_id desc

   DROP TABLE #resultTable

