﻿/*
DECLARE @pBlockUserConfigId int
DECLARE @pEmplacementId int
DECLARE @pBlockEmplacementId int
DECLARE @pBlockFunctionId int
DECLARE @pBlockUserConfigValue varchar(max)

set @pBlockEmplacementId = 1
set @pBlockFunctionId = 1
set @pBlockUserConfigValue = 'chemin'


set @pBlockUserConfigId = 0
select * from HomeModular_BlockUserConfig

*/


--supprime toute la config du block pour l'emplacement
--delete from HomeModular_BlockUserConfig where BlockEmplacement_ID = (select BlockEmplacement_ID from HomeModular_BlockEmplacement where Emplacement_ID = @pEmplacementId)
declare @blockUserConfigId int
set @blockUserConfigId = (select  BlockUserConfig_ID from HomeModular_BlockUserConfig 
								where BlockUserConfig_ID= @pBlockUserConfigId and BlockEmplacement_ID=@pBlockEmplacementId 
								and BlockFunction_ID=@pBlockFunctionId )


if(@pBlockUserConfigId = 0)
BEGIN
	insert into HomeModular_BlockUserConfig values (@pBlockEmplacementId, @pBlockFunctionId, @pBlockUserConfigValue)
END


if(@pBlockUserConfigId > 0)
BEGIN
	update HomeModular_BlockUserConfig set BlockEmplacement_ID = @pBlockEmplacementId, BlockFunction_ID = @pBlockFunctionId, BlockcUserConfig_Value= @pBlockUserConfigValue 
		where BlockUserConfig_ID = @blockUserConfigId
END

