﻿

 declare @typeDatecourte int = 103 -- angl par defaut
 if @@LANGID = 1  begin set @typeDatecourte = 104 end -- de
 if @@LANGID = 2  begin set @typeDatecourte = 103 end -- fr
 if @@LANGID = 7  begin set @typeDatecourte = 105 end -- nl ??

SELECT 

dp.dos_prod_id as dossier_id, dp.dos_prod_id as dossier_numero, 
case when tp.produit_nom is null then p.produit_nom else tp.produit_nom end as produit_nom, 
case when tp.produit_descrip is null then p.produit_descrip else tp.produit_descrip end  as produit_info_1, 

'50' + right('000000' + convert(varchar(50), dp.commande_id), 6)
+  right('000000' + convert(varchar(50), dp.dos_prod_id), 6) +
right('000' + convert(varchar(max),(select count(*) from commande_ligne cl2
inner join recette_produit r2 on r2.produit_stock_id = cl2.dossier_id and r.seance_id = cl2.seance_id  
Where cl2.Commande_id = dp.commande_id and type_ligne ='PRO' and cl2.dossier_id = dp.dos_prod_id )), 3) as CODEBARRE_UNIQUE
,
CONVERT(varchar(20),CAST((dos_prod_montant) as decimal(18,2))) as produit_montant_total_eu ,
CONVERT(varchar(20),CAST((dos_prod_montant1 + dos_prod_montant2) as decimal(18,2))) as produit_montant_total_eu2 , 
CONVERT(varchar(20),CAST(p.vts_grille1 as decimal(18,2))) as produit_montant_eu,
CONVERT(varchar(20),CAST(p.vts_grille2 as decimal(18,2))) as produit_montant_2_eu,
CONVERT(varchar(20),CAST(p.vts_grille3 as decimal(18,2))) as produit_montant_3_eu,
CONVERT(varchar(20),CAST(p.vts_grille4 as decimal(18,2))) as produit_montant_4_eu,
CONVERT(varchar(20),CAST(p.vts_grille5 as decimal(18,2))) as produit_montant_5_eu,
CONVERT(varchar(20),CAST(p.vts_grille6 as decimal(18,2))) as produit_montant_6_eu,
CONVERT(varchar(20),CAST(p.vts_grille7 as decimal(18,2))) as produit_montant_7_eu,
CONVERT(varchar(20),CAST(p.vts_grille8 as decimal(18,2))) as produit_montant_8_eu,
CONVERT(varchar(20),CAST(p.vts_grille9 as decimal(18,2))) as produit_montant_9_eu,
CONVERT(varchar(20),CAST(p.vts_grille10 as decimal(18,2))) as produit_montant_10_eu,
CONVERT(varchar(20),CAST((dos_prod_montant) as decimal(18,2))) as produit_dossier_montant_total_eu, 
p.unbilletparproduit as unbilletparproduit, 
CONVERT(varchar(50), dpr.Date_Deb_Validite, @typeDatecourte)  as DATE_DEB_VALIDITE_KDO,
CONVERT(varchar(50), dpr.Date_Fin_Validite, @typeDatecourte)  as DATE_FIN_VALIDITE_KDO,
dpr.Reference as REF_KDO  
,r.recette_id
,iic.valeur3 as ADHESION_VALIDITE
INTO #tblTemp
FROM produit p 
left join traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id =[LANG_ID]
INNER JOIN dossier_produit dp ON p.produit_id=dp.produit_id 
INNER JOIN recette_produit r on dp.dos_prod_id=r.produit_stock_id
LEFT OUTER JOIN Dossier_Produit_Ref dpr on dpr.Dos_prod_id =dp.dos_prod_id 
LEFT OUTER JOIN identite_infos_comp iic on  iic.info_comp_id = p.infocomp_id and iic.identite_id = dp.identite_id and iic.supprimer = 'N'
 WHERE dp.produit_id=[PRODUCT_ID] AND dp.commande_id=[COMMAND_ID] 

 declare @recettId int = [RECETTE_ID]
 if (@recettId > 0)
    delete #tblTemp where recette_id<>@recettId

     declare @dos_prod_id int = [DOS_PROD_ID]
 if (@dos_prod_id > 0)
    delete #tblTemp where dossier_id<>@dos_prod_id


 select * from #tblTemp
 
  drop table #tblTemp



 