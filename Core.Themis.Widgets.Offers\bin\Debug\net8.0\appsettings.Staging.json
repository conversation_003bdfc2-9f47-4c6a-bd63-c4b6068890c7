{
  "Mode": "Dev",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    }
  },
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=SphereWebTest;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "SitePaiement": "https://payment.themisweb.fr/testv3/payment/optionspayment/[structureid]/[basketid]/[platformname]/[lang]/[haskey]/[identiteid]",
  //charge les traductions filtrées par areas
  "TranslationsAreas": {
    "CrossSelling": [ "Global", "CrossSelling" ],
    "Session": [ "Global", "Session" ],
    "Basket": [ "Global", "Basket" ],
    "Product": [ "Global", "Product" ],
    "HomeModular": [ "Global", "HomeModular" ],
    "Insurance": [ "Global", "Insurance" ],
    "EventsCatalog": [ "Global", "Catalog" ]
  },
  "Cache": {
    //Cache pour la liste des manifestation en secondes
    //"EventsAbsoluteExpiration": 10,
    //"EventsSlidingExpiration": 2,
    //Cache pour la liste des seances (calendrier) en secondes
    "SessionsAbsoluteExpiration": 10,
    "SessionsSlidingExpiration": 2,
    //Cache de la Grille de tarif � chaque changement de zone, etage, section en secondes
    //"GrilleTarifAbsoluteExpiration": 10,
    //"GrilleTarifSlidingExpiration": 2,
    "InsuranceAbsoluteExpiration": 3,
    "TranslationsAbsoluteExpiration": 3,
    "SponsorsAbsoluteExpiration": 10,
    "HomeModularAbsoluteExpiration": 120,

    "AdhesionsAbsoluteExpiration": 120,
    "AdhesionsSlidingExpiration": 2,

    //Cache pour la liste des sièges en secondes
    "SeatsAbsoluteExpiration": 120,
    "SeatsSlidingExpiration": 2,
    "SeatsTextAbsoluteExpiration": 600,
    "SeatsTextSlidingExpiration": 2,
    "ProductsGlobauxToBasket": 2400,
    "ProductsForEvents": 2400,
    "ProductsForSessions": 2400,

    //Cache pour la liste des manifestations (eventsCatalog) en secondes
    "EventsCatalogSlidingExpiration": 120,
    "EventsCatalogAbsoluteExpiration": 150,
    //Cache pour la liste des manifestations (eventsCatalog) au niveau des filtres en secondes
    "EventsCatalogAFiltersbsoluteExpiration": 120,
    "EventsCatalogFilterSlidingExpiration": 150,
    "UpdateCacheFile": "\\\\Srv-paiement64\\CUSTOMERFILES\\TEST\\updatecache.txt"
  },
  "HomeModular": {
    "Products_All_Url": "https://test.{domain}/indiv/BoutiqueHome.aspx?idstructure={structureId}",
    "Products_Family_Url": "https://test.{domain}/indiv/boutiqueFamille.aspx?idstructure={structureId}&fid={familyId}",
    "Products_Subfamily_Url": "https://test.{domain}/indiv/boutiqueSousFamille.aspx?idstructure={structureId}&sfid={subfamilyId}",
    "Product_One_Url": "https://test.{domain}/indiv/boutiqueProduitDetailWidget.aspx?idstructure={structureId}&pid={productId}",
    "Events_All_Url": "https://test.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}",
    "Events_Genre_Url": "https://test.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}&genreid={genreId}",
    "Events_Subgenre_Url": "https://test.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}&subgenreid={subgenreId}",
    "Events_Group_Url": "https://test.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}&groupid={groupId}",
    "Event_One_Url": "https://test.{domain}/indiv/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}&sessionid={sessionId}",
    "Subscription_Url": "https://test.{domain}/abov2/home/<USER>/{langCode}",
    "Events_Featured_Url": "https://test.{domain}/indiv/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}",
    "Products_Featured_Url": "https://test.{domain}/indiv/boutiqueProduitDetailWidget.aspx?idstructure={structureId}&pid={productId}",
    "CustomerArea_Url": "https://test.{domain}/customerv3/home.aspx?idstructure={structureId}"
  },
  "EventsCatalog": {
    "Event_One_Url": "https://test.{domain}/indiv/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}",
    "Abo_One_Url": "https://test.{domain}/indiv/fChoixSeanceAbo.aspx?idstructure={structureId}&FormulaId={formulaId}",
    "WaitingList_Url": "https://test.{domain}/customerv3/WaitList.aspx?idstructure={structureId}&page=waitlist&EventId={eventId}&disconnect=0&iswidget=1"
  },
  "WidgetCatalogUrl": "https://test2.themisweb.fr/widgets/catalog/current/",
  "WidgetOfferUrl": "https://test2.themisweb.fr/widgets/offers/current/",
  "WidgetCustomerUrl": "https://test2.themisweb.fr/widgets/customers/current/",
  
  "ApiAuthenticationUrl": "https://staging.rodrigue-ws.com/current/authentication/api/",

  "identiteSalt": "none",
  "TypeRun": "TEST",
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  // "PathForSqlScript": "..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\{structureId}\\CONFIGSERVER\\config.ini.xml",

  "EventsImagesUrlPath": "https://test.themisweb.fr/files/{structureId}/INDIV/images/manifestations/",
  "RodriguePartnerName": "RODRIGUE",

  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "AudienceToken": "ThemisAPI",
  "CryptoKey": "RodWebShop95",
  "PathBase": "/widgets/offers/current/",
  "SeatPlan": {
    "SeatingPlanPerspectivePhysicalPath": "d:\\customerfiles\\TEST\\{structureId}\\INDIV\\IMAGES\\seatingplans\\iindexToXY_perspect[param].xml",
    "SeatingPlanUrlPath": "https://test.themisweb.fr/files/{structureId}/INDIV/images/seatingplans/"
  },

  "Images": {
    "PanoUrlPath": "https://test.themisweb.fr/files/{structureId}/INDIV/images/pano/",
    "BaseImagesPhysicalPath": "d:\\customerfiles\\TEST\\{structureId}\\INDIV\\IMAGES\\",
    "EventsImagesPhysicalPath": "D:\\CUSTOMERFILES\\TEST\\{structureId}\\INDIV\\IMAGES\\manifestations\\",
    "EventsImagesUrlPath": "https://test.themisweb.fr/files/{structureId}/INDIV/images/manifestations/",
    "ProductsImagesPhysicalPath": "D:\\CUSTOMERFILES\\TEST\\{structureId}\\INDIV\\IMAGES\\produits\\",
    "ProductsImagesUrlPath": "https://test.themisweb.fr/files/{structureId}/INDIV/images/produits/",
    "BaseImagesUrlPath": "/files/{structureId}/INDIV/images/",
    "AssetsImagesUrlPath": "https://test.themisweb.fr/assets/IMAGES/indiv/"
  },

  "AssetsUrlPath": "https://test.themisweb.fr/assets/",
  "PanoFileVersion": "pano1.20.9.js",
  "physicalPathOfSettingsJSON": "d:\\customerfiles\\TEST\\{structureId}{plateformCode}\\appsettings.json",
  "CustomerfilesIndivPhysicalPath": "d:\\customerfiles\\TEST\\{structureId}\\INDIV\\",
  "CustomerfilesUrlPath": "https://test.themisweb.fr/files/{structureId}/INDIV/",
  "Sponsor": {
    "SponsorAdvantageCardSalt": "7f5a54f374604d362034ba883d9ac38dc4a8f00f8dc708138135d242457a4e49"
  }
}
