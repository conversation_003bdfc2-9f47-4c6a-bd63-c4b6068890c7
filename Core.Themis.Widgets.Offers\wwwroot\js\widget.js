﻿var SphereOffers;
(function () {

    // Localize jQuery variable
    var jQuery;

    /******** Load jQuery if not present *********/
    if (window.jQuery === undefined || compareversion(window.jQuery.fn.jquery, '3.3.1')) {

        /*var script_tag = document.createElement('script');
        script_tag.setAttribute("type", "text/javascript");
        script_tag.setAttribute("src", "https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js");
        if (script_tag.readyState) {
          script_tag.onreadystatechange = function() { // For old versions of IE
            if (this.readyState == 'complete' || this.readyState == 'loaded') {
              scriptLoadHandler();
            }
          };
        } else { // Other browsers
          script_tag.onload = scriptLoadHandler;
        }
        // Try to find the head, otherwise default to the documentElement
        (document.getElementsByTagName("head")[0] || document.documentElement).appendChild(script_tag);*/
    } else {
        // The jQuery version on the window is the one we want to use
        jQuery = window.jQuery;
        init();
    }

    function compareversion(version1, version2) {

        var result = false;

        if (typeof version1 !== 'object') {
            version1 = version1.toString().split('.');
        }
        if (typeof version2 !== 'object') {
            version2 = version2.toString().split('.');
        }

        for (var i = 0; i < (Math.max(version1.length, version2.length)); i++) {

            if (version1[i] == undefined) {
                version1[i] = 0
            }
            if (version2[i] == undefined) {
                version2[i] = 0;
            }

            if (Number(version1[i]) < Number(version2[i])) {
                result = true;
                break;
            }
            if (version1[i] != version2[i]) {
                break;
            }
        }
        return (result);
    }

    /******** Called once jQuery has loaded ******/
    function scriptLoadHandler() {
        // Restore $ and window.jQuery to their previous values and store the
        // new jQuery in our local jQuery variable
        jQuery = window.jQuery.noConflict(true);
        // Call our main function
        init();
    }

    /******** Création de l'iframe ******/
    function iframeCreator(selector, action, data) {
        var selectorCleaned = selector.replace(/[^a-zA-Z0-9]/g, "");
        var parentBody = this.document.body;
        jQuery(parentBody).find(selector).html("<iframe id='sWOffers_" + selectorCleaned + "' data-action='" + action + "' src='" + sWOffersUrl + "" + data + "' " +
            " width = '100%' height = '0' frameborder = '0' scrolling = 'no' allowfullcreen='true' /></iframe > ");
        //+ " width = '100%' height = '100%' frameborder = '1px' scrolling = 'yes' /></iframe> ");
        openWaitingInside(selector);
        document.getElementById("sWOffers_" + selectorCleaned).onload = function () {
            //console.log("#sWOffers_" + selectorCleaned + " loaded");
            closeWaitingInside(selector);
            $(window).resize();
        };
        window.removeEventListener('message', iframeListener);
        window.addEventListener('message', iframeListener);

    }

    /******** Écoute de l'iframe ******/
    function iframeListener(event) {
        //console.log(event.data);
        switch (event.data.action) {
            case ("resize"):
                $("#sWOffers_" + decodeURIComponent(event.data.selector).replace(/[^a-zA-Z0-9]/g, "")).css({
                    "height": event.data.height
                });
                break;
            case ("urltogo"):
                window.location = event.data.url;
                break;
        }
        //pour les resize en cascade
        if (typeof sendIframeSize !== "undefined") {
            sendIframeSize();
        }

    }

    function openWaitingInside(target) {
        var html = '';
        html += '<style>' +
            '@-webkit-keyframes loading{0%{opacity:1;-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes loading{0%{opacity:1;-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}' +
            '</style>' +
            '<svg version="1.1" style="width:60px;position: relative; margin: 0 auto; display: block;" id="loadingcircle" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 745.5 745.5" style="enable-background:new 0 0 745.5 745.5;" xml:space="preserve">' +
            '<g id="colorcircleWrapper" style="transform-origin: 50% 50%;  -webkit-animation: loading .65s linear  infinite; animation: loading .65s  linear infinite; -webkit-transform-origin: center; transform-origin: center;">' +
            '<path id="colorcircle" d="M456.44,148.85l0.01-0.02c-26.15-9.85-54.49-15.24-84.09-15.24c-131.72,0-238.5,106.78-238.5,238.5        c0,26.61,4.36,52.22,12.41,76.11l0.02-0.01c4.52,13.84,17.37,23.98,32.76,24.35c19.51,0.46,35.7-14.98,36.15-34.49        c0.09-4.12-0.52-8.1-1.73-11.81c-0.04-0.11-0.08-0.22-0.12-0.33c-0.06-0.21-0.13-0.42-0.21-0.63c-5.58-16.71-8.6-34.6-8.6-53.19        c0-92.68,75.13-167.82,167.82-167.82c20.04,0,39.25,3.51,57.07,9.95l0.04-0.09c4.09,1.54,8.53,2.38,13.18,2.38        c20.07,0,36.33-15.73,36.33-35.14C478.97,166.67,469.67,154.1,456.44,148.85z" />' +
            '<path id="colorcircleNotVisible" d="M288.27,596.38l-0.01,0.02c26.15,9.85,54.49,15.24,84.09,15.24c131.72,0,238.5-106.78,238.5-238.5        c0-26.61-4.36-52.22-12.41-76.11l-0.02,0.01c-4.52-13.84-17.37-23.98-32.76-24.35c-19.51-0.46-35.7,14.98-36.15,34.49        c-0.09,4.12,0.52,8.1,1.73,11.81c0.04,0.11,0.08,0.22,0.12,0.33c0.06,0.21,0.13,0.42,0.21,0.63c5.58,16.71,8.6,34.6,8.6,53.19        c0,92.68-75.13,167.82-167.82,167.82c-20.04,0-39.25-3.51-57.07-9.95l-0.04,0.09c-4.09-1.54-8.53-2.38-13.18-2.38        c-20.07,0-36.33,15.73-36.33,35.14C265.74,578.55,275.05,591.12,288.27,596.38z" />' +
            '</g>' +
            '</svg>';
        $(target).append(html);
    }

    function closeWaitingInside(target) {
        $(target).find('#loadingcircle').remove();
    }

    /******** Our main function ********/
    function init() {

       
        SphereOffers = {
      
            Catalog: function (options) {
                // Default options
                var settings = jQuery.extend({
                    selector: '',
                    partnerName: '',
                    structureId: 0,
                    langCode: '',
                    identityId: '',
                    buyerProfilId: '',
                    catalogId: null,
                    eventGroupId: null,
                    genreId: null,
                    subGenreId: null,
                    targetId: null,
                    search: null,
                    identiteHash: '',
                    linkToEvent: '',
                    signature: '',
                }, options);
                // Apply options

                $.ajax({
                    type: "POST",
                    url: `${sWOffersUrl}Catalog`,
                    //url: `${sWOffersUrl}EventsView/${settings.structureId}/${settings.langCode}/${settings.identityId}/${settings.buyerProfilId}`,
                    dataType: "json",
                    data:  {
                       structureId : settings.structureId,
                        langCode: settings.langCode,
                        identityId: settings.identityId,
                        buyerProfilId: settings.buyerProfilId,
                        catalogId: settings.catalogId,
                        eventGroupId: settings.eventGroupId,
                        genreId: settings.genreId,
                        subGenreId: settings.subGenreId,
                        targetId: settings.targetId,
                        search: settings.search,
                        identiteHash: settings.identiteHash,
                        partnerName: settings.partnerName,
                        linkToEvent: encodeURIComponent(settings.linkToEvent),
                        htmlSelector: encodeURIComponent(settings.selector)
                    },
                    headers: {
                        "Signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "Catalog", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("Catalog -> Error");
                        return jQuery(settings.selector).html(a.responseText);

                    }
                });

            },

            iTunnel: function (options) {
                var settings = jQuery.extend({
                    selector: 'body',
                    partnerName: '',
                    structureId: 0,
                    langCode: '',
                    eventId: 0,
                    identityId: 0,
                    webUserId: 0,
                    buyerProfilId: 0,
                    forceSession: 0,
                    forceDate: "0",
                    signature: '',
                    mySettings: '',
                }, options);
                // Apply options
                jQuery.ajax({
                    type: "POST",
                    url: sWOffersUrl + "itunnel",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        langCode: settings.langCode,
                        eventId: settings.eventId,
                        identityId: settings.identityId,
                        webUserId: settings.webUserId,
                        buyerProfilId: settings.buyerProfilId,
                        forceSession: settings.forceSession,
                        forceDate: settings.forceDate,
                        partnerName: settings.partnerName,
                        htmlSelector: encodeURIComponent(settings.selector),
                        mySettings: encodeURIComponent(settings.mySettings)
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"

                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "Tunnel", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("Tunnel -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },


            Basket: function (options) {
                var settings = jQuery.extend({
                    selector: 'body',
                    partnerName: '',
                    structureId: 0,
                    langCode: '',
                    basketId: 0,
                    lasteventId: 0,                    
                    identityId: 0,
                    buyerProfilId: 0,
                    webUserId: 0,
                    signature: '',
                    mySettings: '',
                    useContext: 'indiv'

                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "Basket",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        langCode: settings.langCode,
                        basketId: settings.basketId,
                        lasteventId: settings.lasteventId,
                        identityId: settings.identityId,
                        webUserId: settings.webUserId,
                        buyerProfilId: settings.buyerProfilId,
                        partnerName: settings.partnerName,
                        useContext: settings.useContext,
                        htmlSelector: encodeURIComponent(settings.selector),
                        mySettings: encodeURIComponent(settings.mySettings)
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"

                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "Basket", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("Basket -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },

            MiniBasket: function (options) {
                var settings = jQuery.extend({
                    selector: 'body',
                    partnerName: '',
                    structureId: 0,
                    langCode: '',
                    basketId: 0,
                    lasteventId: 0,
                    identityId: 0,
                    buyerProfilId: 0,
                    webUserId: 0,
                    signature: '',
                    mySettings: '',
                    useContext: 'indiv'

                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "MiniBasket",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        langCode: settings.langCode,
                        basketId: settings.basketId,
                        lasteventId: settings.lasteventId,
                        identityId: settings.identityId,
                        webUserId: settings.webUserId,
                        buyerProfilId: settings.buyerProfilId,
                        partnerName: settings.partnerName,
                        useContext: settings.useContext,
                        htmlSelector: encodeURIComponent(settings.selector),
                        mySettings: encodeURIComponent(settings.mySettings)
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"

                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "MiniBasket", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("Mini Basket -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },
       
            ProductDetail: function (options) {
                var settings = jQuery.extend({
                    selector: 'body',
                    partnerName: '',
                    structureId: 0,
                    langCode: '',
                    productId: 0,
                    identityId: 0,
                    webUserId: 0,
                    buyerProfilId: 0,
                    signature: '',
                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "ProductDetail",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        langCode: settings.langCode,
                        productId: settings.productId,
                        identityId: settings.identityId,
                        webUserId: settings.webUserId,
                        buyerProfilId:settings.buyerProfilId,
                        partnerName: settings.partnerName,
                        htmlSelector: encodeURIComponent(settings.selector),
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"

                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "ProductDetail", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("ProductDetail -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },

            Session: function (options) {
                var settings = jQuery.extend({
                    selector: 'body',
                    partnerName: '',
                    structureId: 0,
                    langCode: '',
                    eventId: 0,
                    identityId: 0,
                    webUserId: 0,
                    buyerProfilId: 0,
                    forceSession: 0,
                    forceDate: "0",
                    signature: '',
                    mySettings: '',
                    useContext: 'indiv'
                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "Session",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        langCode: settings.langCode,
                        eventId: settings.eventId,
                        identityId: settings.identityId,
                        webUserId: settings.webUserId,
                        buyerProfilId: settings.buyerProfilId,
                        forceSession: settings.forceSession,
                        forceDate: settings.forceDate,
                        partnerName: settings.partnerName,
                        useContext: settings.useContext,
                        mySettings: encodeURIComponent(settings.mySettings),
                        htmlSelector: encodeURIComponent(settings.selector),
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"

                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "Session", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("Session -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },

            CrossSelling: function (options) {
                var settings = jQuery.extend({
                    structureId: 0,
                    buyerProfilId: 0,
                    htmlSelector: 'body',
                    partnerName: '',
                    eventIds: [],
                    sessionIds: [],
                    poidsGenre: 100,
                    poidsSousGenre: 100,
                    poidsCible: 80,
                    poidsMois: 10,
                    differenceDate: 0,
                    poidsGroupe: 100,
                    nombreARemonter: 3,
                    langCode: '',
                    eventsUrl: '',
                    eventsImagesUrl: '',
                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "CrossSelling",
                    dataType: "json",
                    data: {                        
                        structureId: settings.structureId,
                        buyerProfilId: settings.buyerProfilId,
                        htmlSelector: encodeURIComponent(settings.selector),
                        partnerName: settings.partnerName,
                        eventIds: settings.eventIds.toString(),
                        sessionIds: settings.sessionIds.toString(),
                        poidsGenre: settings.poidsGenre,
                        poidsSousGenre: settings.poidsSousGenre,
                        poidsCible: settings.poidsCible,
                        poidsMois : settings.poidsMois,
                        differenceDate: settings.differenceDate,
                        poidsGroupe: settings.poidsGroupe,
                        nombreARemonter: settings.nombreARemonter,
                        langCode : settings.langCode,
                        eventsUrl : settings.eventsUrl,
                        eventsImagesUrl: settings.eventsImagesUrl,
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"

                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "CrossSelling", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("CrossSelling -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },
            HomeModular: function (options) {
                var settings = jQuery.extend({
                    structureId: 0,
                    buyerProfilId: 0,
                    selector: 'body',
                    partnerName: '',
                    langCode: '',
                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "HomeModular",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        buyerProfilId: settings.buyerProfilId,
                        htmlSelector: encodeURIComponent(settings.selector),
                        partnerName: settings.partnerName,
                        langCode: settings.langCode,
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "HomeModular", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("HomeModular -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },
            Insurance: function (options) {
                var settings = jQuery.extend({
                    structureId: 0,
                    langCode: '',
                    basketId : 0,
                    webUserId: 0,
                    buyerProfilId: 0,
                    partnerName: '',
                    selector: 'body',
                }, options);
                // Apply options
                jQuery.ajax({
                    type: "GET",
                    url: sWOffersUrl + "Insurance",
                    dataType: "json",
                    data: {
                        structureId: settings.structureId,
                        langCode: settings.langCode,
                        basketId: settings.basketId,
                        webUserId: settings.webUserId,
                        buyerProfilId: settings.buyerProfilId,
                        partnerName: settings.partnerName,
                        htmlSelector: encodeURIComponent(settings.selector),
                    },
                    headers: {
                        "signature": settings.signature,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    success: function (data) {
                        iframeCreator(settings.selector, "Insurance", data.responseText);
                    },
                    error: function (a, b, c) {
                        console.log("Insurance -> Error");
                        jQuery(settings.selector).html(a.responseText);
                    }
                });
            },
        }
    }
})();


/**
* Init window.onmessage event function
* @param {EventListener} e 
*/
window.onmessage = function (e) {
    if (e.data.action == 'showCustomModal') {
        createCustomModalFromHtml(new ModalInfos(e.data.modalInfos));
    } else if (e.data.action == 'showToast') {
        initShowCustomToast(e.data.toastHtml)
    } else if (e.data.action == 'scrollTo') {
        window.scroll({ top: e.data.offsetTop, behavior: 'smooth' });
    }
};

/** add toast to the dom body */
function initShowCustomToast(toastHtml) {
    selectorWrapper = "#widget-rodrigue-wrapper";
    if ($(selectorWrapper).length == 0) {
        $("body").append("<div id='widget-rodrigue-wrapper'></div>");
    }
    var toastId = $(toastHtml).attr('id');
    var toastPositionClass = $(toastHtml).attr('data-wdgt-toast-class-position');
    var toastDelay = $(toastHtml).attr('data-wdgt-toast-delay');

    toastPositionClass = toastPositionClass.replace(/\s\s+/g, ' ');
    selectorToastContainer = selectorWrapper + " ." + toastPositionClass.split(' ').join('.');
    if ($(selectorToastContainer).length == 0) {
        $(selectorWrapper).append("<div class='toast-container p-3 position-fixed " + toastPositionClass + "'></div>");
    }


    $(selectorToastContainer).prepend(toastHtml);
    //$('#' + toastId).addClass('showing').addClass('show');//.delay(150).addClass('show').delay(150).removeClass('showing');
    $('#' + toastId).addClass('showing').addClass('show').delay(150).queue(function (next) {
        $(this).removeClass('showing').dequeue();
    });
    $('[data-wdgt-dismiss="toast"]').off('click').on('click', function () {
        var id = $(this).closest('.toast').attr("id");
        initHideCustomToast(id);
    });
    //if auto-hide

    if (toastDelay != "" && toastDelay != undefined) {
        toastDelay = parseInt(toastDelay);//en milisecondes
        setTimeout(() => {
            initHideCustomToast(toastId);
        }, toastDelay);
    }

}
function initHideCustomToast(toastId) {
    $('#' + toastId).addClass('showing').delay(150).queue(function (next) {
        $(this).remove().dequeue();
    });
}
/**
 * Add modal to the dom body
 * @param {ModalInfos} modalInfo 
 */
function createCustomModalFromHtml(modalInfo) {
    initShowCustomModal(modalInfo.htmlContent, modalInfo.modalIdSelector);

    if (modalInfo.isConfirmation == false)
        handleSubmitCustomModalForm(modalInfo);
}

//ovuerture d'une modal
function initShowCustomModal(modalHtml, modalId) {
    selector = "#widget-rodrigue-wrapper";
    if ($("#widget-rodrigue-wrapper").length == 0) {
        $("body").append("<div id='widget-rodrigue-wrapper'></div>");

    }
    $(selector).append(modalHtml);
    var backdrop_html = `<div class="widget-modal-backdrop fade show"></div>`;
    $(selector).append(backdrop_html);
    $("#" + modalId).addClass("show").show();
    $('body').addClass("widget-modal-open");
    $(".widget-modal-backdrop").addClass("show");
    $("#" + modalId).find('[data-wdgt-dismiss="modal"]').off('click').on('click', function () {
        var id = $(this).closest('widget-modal').attr("id");
        initHideCustomModal(id);
    })
}
//fermeture d'une modal
function initHideCustomModal(modalId) {
    $("#" + modalId).removeClass("show").hide();
    $(".widget-modal-backdrop").remove();
    $('body').removeClass("widget-modal-open");
}

function handleSubmitCustomModalForm(customModalModel) {
    let form = document.getElementById(customModalModel.formIdSelector);

    form.onsubmit = async (e) => {
        e.preventDefault();

        let formData = new FormData(form);

        let result = {};

        for (const [key, value] of formData) {
            let valueToUse = value == "on" ? true : value;

            if (key.includes("[")) {
                let keyToUse = key.split("[")[0];
                let index = key.split("[")[1].split("]")[0];
                let prop = key.split(".")[1];

                console.log(keyToUse);
                console.log(index);
                console.log(prop);

                if (result[keyToUse] == null)
                    result[keyToUse] = [];

                if (result[keyToUse][index] == null)
                    result[keyToUse].push({});

                result[keyToUse][index][prop] = valueToUse;

            } else {
                result[key] = valueToUse
            }
        };

        sendCustomModalResponse(customModalModel.iframeSelector, customModalModel.modalIdSelector, JSON.stringify(result));
    };
}

async function handleChangeCallNewContent(route, areaSelector) {
    await fetch(route)
        .then((response) => response.text())
        .then((result) => $(areaSelector).html(result));
}

/**
 * Send confirmation response to iframe
 * @param {string} iframeSelector 
 * @param {string} modalSelector 
 * @param {boolean} response 
 */
function sendConfirmationModalResponse(iframeSelector, modalIdSelector, response) {

    var message = {
        "action": "confirmation",
        "response": response
    };

    sendMessageToIframe(iframeSelector, message);

    initHideCustomModal(modalIdSelector);

    var modal = document.getElementById(modalIdSelector);
    modal.remove();
}

/**
 * Send form response to iframe
 * @param {string} iframeSelector 
 * @param {string} modalSelector 
 * @param {string} response 
 */
function sendCustomModalResponse(iframeSelector, modalIdSelector, response) {

    var message = {
        "action": "customResponse",
        "response": response
    };

    initHideCustomModal(modalIdSelector);

    sendMessageToIframe(iframeSelector, message);

    var modal = document.getElementById(modalIdSelector);
    modal.remove();
}

function sendCustomModalredirection(iframeSelector, modalIdSelector, Redirection) {

    var message = {
        "action": "customRedirection",
        "response": Redirection
    };

    var modal = document.getElementById(modalIdSelector);
    modal.remove();


    initHideCustomModal(modalIdSelector);

    sendMessageToIframe(iframeSelector, message);

    
}


/**
 * Send message to iframe
 * @param {string} iframeSelector 
 * @param {object} message 
 */
function sendMessageToIframe(iframeSelector, message) {
    var myIframe = document.querySelector(iframeSelector);
    myIframe.contentWindow.postMessage(message, '*');
}

class ModalInfos {
    constructor(modalInfo) {
        this.modalIdSelector = modalInfo.modalIdSelector;
        this.iframeSelector = modalInfo.iframeSelector;
        this.formIdSelector = modalInfo.formIdSelector;
        this.htmlContent = modalInfo.htmlContent;
        this.isConfirmation = modalInfo.isConfirmation;
    }
}