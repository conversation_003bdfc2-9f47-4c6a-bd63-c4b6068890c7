﻿/* calendrier : manif et seances */

/*
declare @pidentityId int = 0
declare @pbuyerprofilId int = 13
*/

declare @myidentite int 
set @myidentite = @pidentityId
declare @mybuyerprofilId int
set @mybuyerprofilId = @pbuyerprofilId

declare @iamrevendeur int = 0;
select @iamrevendeur = is_revendeur from profil_acheteur pa where id = @mybuyerprofilId 

CREATE TABLE #myoffres 
( 
	offre_id  INT, 
	offre_nom VARCHAR(200) 
) 
EXEC [Sp_ws_getoffres_totable] 
@myidentite, 
@mybuyerprofilId 

declare @offreCount int
select @offreCount =count(*) from #myoffres;

declare @manif_id int
declare @sql varchar(max)

--CREATE TABLE #myManifs (manif_id int)
create TABLE #myManifsGpIds (manif_id int, gestion_place_id int)


IF (@offreCount> 0)	
BEGIN
			INSERT INTO #myManifsGpIds 
			select distinct manif_id, gp.gestion_place_id from gestion_place gp
			inner join offre_gestion_place gpo on gpo.gestion_place_id = gp.gestion_place_id 
			inner join seance s on s.seance_id=gp.seance_id 
			inner join #myoffres myoff on myoff.offre_id = gpo.offre_id
				where gp.isvalide=1 and s.seance_date_fin>getdate()
END

if (@iamrevendeur=0)
begin


	insert into #myManifsGpIds select distinct manif_id, gestion_place_id from gestion_place gp 
			inner join seance s on s.seance_id=gp.seance_id 
			where isvalide=1 and
				isContrainteIdentite=0 and s.seance_date_fin>getdate()
				
	INSERT INTO #myManifsGpIds /* add les gestion places des offres d'adhesion mises en avant */
						select distinct gp.manif_id, gp.gestion_place_id 
						from offre o
						inner join offre_gestion_place ogp on ogp.offre_id = o.offre_id
						
						inner join gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id
						inner join seance s on s.seance_id=gp.seance_id 
						inner join Adhesion_Catalog_offresliees ol on ol.offre_id = o.offre_id
						inner join Adhesion_Catalog acat on acat.Adhesion_Catalog_ID = ol.Adhesion_Catalog_ID

                        inner join Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = acat.Adhesion_Catalog_ID and gp.type_tarif_id = acprop.Propriete_Valeur_Int1

		where acat.Mise_En_Avant =1 and isvalide =1	and s.seance_date_fin>getdate()	
end


SELECT m.manifestation_nom as event_name, m.manifestation_id as event_id, m.manifestation_code as event_code, 
m.manifestation_groupe_id, mgroup.manif_groupe_nom, 
sp.id_super_groupe as super_groupe_id, sp.libelle as super_groupe_nom,
s.seance_id as session_id, 
s.seance_date_deb as date_start, 
s.seance_date_fin as date_end,
l.lieu_id as place_id, l.lieu_nom as place_name, l.lieu_code as place_code,
l.lieu_rue1, l.lieu_rue2, l.lieu_rue3, l.lieu_rue4, lieu_cp, lieu_ville, lieu_region, lieu_pays,
s.lieu_config_id as config_id,
lc.lieu_config_nom,
CASE WHEN NEPASAFFICHERDATE like 'O' THEN 0 ELSE 1 END as isShowSessionDate,
CASE WHEN NEPASAFFICHERDATE like 'H' THEN 0 WHEN NEPASAFFICHERDATE like 'O' THEN 0 ELSE 1 END as isShowSessionHour,
                       
(select islock from GP_MANIFESTATION gpm where gpm.manifestation_id = s.manifestation_id) as is_lock,
isnull(mgg.id, 0) as genre_id, 
ISNULL(m.ID_genre , 0) as sous_genre_id
,prod.producteur_id, prod.producteur_nom, prod.num_licence1, 
prod.num_licence2, 
prod.num_licence3, 
prod.num_licence4, 
prod.num_licence5,
m.manifestation_descrip,
m.manifestation_descrip2,
m.manifestation_descrip3,
m.manifestation_descrip4,
m.manifestation_descrip5,
minf.duree,
minf.resume_manif,
minf.mise_en_scene,
minf.distribution,

discipl.id as discipline_id,
discipl.nom as discipline_nom,
loca.code as loca_code,
loca.nom as loca_nom,
loca.id as loca_id

--gt.*, gp.*  
FROM gestion_place gp 
INNER JOIN #myManifsGpIds mygp on mygp.gestion_place_id = gp.gestion_place_id
INNER JOIN seance s on gp.seance_id = s.seance_Id and s.manifestation_id = gp.manif_id
inner JOIN lieu_configuration lc on lc.lieu_config_id = s.lieu_config_id
INNER JOIN lieu l on s.lieu_id = l.lieu_id
INNER JOIN manifestation m ON m.manifestation_id=s.manifestation_id 
INNER JOIN manifestation_groupe mgroup on mgroup.manif_groupe_id = m.manifestation_groupe_id
inner join super_groupe sp on sp.id_super_groupe = mgroup.super_groupe_id

LEFT OUTER JOIN manifestation_genre mg on mg.id = m.ID_genre
LEFT OUTER JOIN manifestation_groupe_genre mgg on mgg.id = mg.groupe_id
LEFT OUTER JOIN producteur prod on prod.producteur_id = m.producteur_id
LEFT OUTER JOIN manifestation_infos minf on minf.manifestation_id = m.manifestation_id

LEFT OUTER JOIN Manif_Competences mcomp on mcomp.manifestation_id = m.manifestation_id
LEFT OUTER JOIN Discipline discipl on discipl.id = mcomp.Discipline_id

LEFT OUTER JOIN Seance_Localisation sloca on sloca.seance_id = s.seance_id
LEFT OUTER JOIN Localisation loca on loca.id = sloca.Localisation_id

INNER JOIN structure on 1=1 
WHERE gp.isvalide =1 and m.supprimer='N' and s.supprimer='N'
GROUP BY m.manifestation_nom, s.manifestation_id, m.manifestation_id, manifestation_code, m.manifestation_groupe_id, 
sp.id_super_groupe, sp.libelle,
s.seance_id, s.seance_date_deb, 
s.seance_date_fin, s.lieu_config_id,
l.lieu_id,l.lieu_nom , l.lieu_code, 
l.lieu_rue1, l.lieu_rue2, l.lieu_rue3, l.lieu_rue4, lieu_cp, lieu_ville, lieu_region, lieu_pays,
lc.lieu_config_nom,
NEPASAFFICHERDATE, m.ID_genre,mgg.id
,prod.producteur_id,
prod.producteur_nom, prod.num_licence1, 
prod.num_licence2, 
prod.num_licence3, 
prod.num_licence4, 
prod.num_licence5,
m.manifestation_descrip,
m.manifestation_descrip2,
m.manifestation_descrip3,
m.manifestation_descrip4,
m.manifestation_descrip5,



minf.mise_en_scene,
minf.distribution,
minf.duree,
minf.resume_manif,
discipl.id, discipl.nom ,
loca.code , loca.nom, loca.id,
mgroup.manif_groupe_nom

ORDER BY m.manifestation_id, s.seance_date_deb --, cat.pref_affichage, tt.pref_affichage

DROP TABLE #myManifsGpIds

DROP TABLE #myoffres

