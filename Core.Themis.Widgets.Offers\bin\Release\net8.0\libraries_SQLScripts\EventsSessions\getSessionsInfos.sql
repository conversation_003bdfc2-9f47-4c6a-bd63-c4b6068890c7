/*
declare @plangCode varchar(2) = 'fR'
*/
DECLARE @langue_Id int = 0
SELECT @langue_Id = langue_id FROM langue WHERE  upper(langue_code) = upper(@plangcode);
IF @langue_Id is null
	set @langue_Id = 0


SELECT s.seance_id, s.seance_date_deb, s.manifestation_id, tva.tva_libelle, tva.tva_taux, l.lieu_id, l.lieu_nom, 
m.manifestation_nom,
l.lieu_code, l.lieu_rue1, l.lieu_rue2, l.lieu_rue3, l.lieu_rue4, l.lieu_cp, l.lieu_ville, l.lieu_region, l.lieu_pays, NePasAfficherDate,
CASE 
	WHEN NEPASAFFICHERDATE like 'O' THEN 0 ELSE 1 END as isShowSessionDate,
CASE 
	WHEN NEPASAFFICHERDATE like 'H' THEN 0 WHEN NEPASAFFICHERDATE like 'O' THEN 0 ELSE 1 END as isShowSessionHour,
LTRIM(RTRIM(m.manifestation_code_numseance)) + LTRIM(RTRIM(s.seance_numero)) as seance_code,
(
	SELECT nom FROM Cible WHERE id = (SELECT Top 1 Cible_id FROM Seance_Cible WHERE Seance_id in ({sessionsids}) AND masquer = 'N' )
) as version_langue_cnc,
s.seance_commentaire,
s.seance_cloturer, 
s.seance_verrouiller,
s.seance_masquer,
s.seance_niveau,
m.manifestation_code as manif_code
FROM seance s
INNER JOIN manifestation m on m.manifestation_id =s.manifestation_id
INNER JOIN tva ON tva.tva_id = s.taux_tva1_id
INNER JOIN lieu l ON l.lieu_id = s.lieu_id
WHERE seance_id IN({sessionsids})
ORDER BY seance_id

 /*
 SELECT s.seance_date_deb, s.seance_date_fin, s.seance_numero, s.seance_commentaire, s.seance_id,
ltrim(rtrim(m.manifestation_code_numseance)) + ltrim(rtrim(s.seance_numero)) as seance_code,
(SELECT nom FROM Cible WHERE id = (SELECT Top 1 Cible_id FROM Seance_Cible WHERE Seance_id=[SEANCE_ID]) AND masquer = 'N' ) as version_langue_cnc
FROM seance s
INNER JOIN manifestation m on m.manifestation_id =s.manifestation_id
WHERE s.seance_id=[SEANCE_ID]
*/