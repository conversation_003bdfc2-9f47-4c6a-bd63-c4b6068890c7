﻿declare
@datestart datetime,
@dateend datetime

/*
declare
@pQuestionCode varchar(max),
@pStructureid varchar(3),
@pStartDate varchar(20),
@pEndDate varchar(20),
@pLstSaleChannels varchar(max)



set @pStructureId = 991
set @pQuestionCode = 'Q1'
set @pStartDate= ''
set @pEndDate= ''

*/

--défini des valeurs par défaut si les dates ne sont pas renseignées
set @datestart = DATEADD(DAY, -7, getdate()) -- FORMAT(GETDATE()-7, 'dd/MM/yyyy') --
set @dateend = GETDATE()


if @pStartDate <> ''
BEGIN
	set @datestart= @pStartDate
END


if @pEndDate <> ''
BEGIN
	set @dateend =@pEndDate
END

declare @SQL varchar(max) 
DECLARE @cnt int --compteur
DECLARE @cntMax int --compteur max
declare @intStr varchar(max) --variable permettant de convertir l'alias dynamic en string
set @cnt = 1 
set @cntMax = 5 

set @SQL = 'SELECT DISTINCT'
WHILE @cnt <= @cntMax
BEGIN
set @intStr = CONVERT(varchar(10),@cnt)

	
	set @SQL = @SQL + '(select COUNT(score) from OpinionOrderForms frm 
		inner join OpinionOrderFormsResponses resp on resp.form_id = frm.form_id 
		inner join OpinionOrderQuestions eq on resp.question_id = eq.id
		inner join OpinionOrderQuestionsTypes eqt on eqt.id = eq.question_type_id
		where structure_id = '+CONVERT(varchar(10), @pStructureId)+' and eq.code = '''+ @pQuestionCode+''' and score = '+@intStr+' '
		
		IF @pLstSaleChannels <> ''
		BEGIN 
		set @SQL = @SQL + 'and filiere_id in ('+@pLstSaleChannels+') '
		END
		
		set @SQL = @SQL + 'and date_response between '''+convert(varchar(max),FORMAT(@datestart, 'dd/MM/yyyy 00:00:00') )+''' and '''+convert(varchar(max),FORMAT(@dateend, 'dd/MM/yyyy 23:59:59') )+''' ) as ['+@intStr+']'
	
		
	if @cnt <> @cntMax 
	begin
		set @SQL =  @SQL +', '
	end

set @cnt = @cnt+1
END
print(@SQL)
exec (@SQL)


select convert(varchar(max),FORMAT(@datestart, 'dd-MM-yyyy') ) as start_date, convert(varchar(max),FORMAT(@dateend, 'dd-MM-yyyy') ) as end_date
