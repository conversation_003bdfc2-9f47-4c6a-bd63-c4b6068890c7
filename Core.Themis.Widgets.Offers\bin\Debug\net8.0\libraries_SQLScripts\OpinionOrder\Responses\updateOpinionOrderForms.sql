﻿/*declare @orderId int
set @orderid = 1850

declare @structureId int
set @structureId = 994
*/
declare @nb_rows_responses int
declare @nb_rows int
declare @form_id int
--declare @comment varchar(max)


set @nb_rows_responses = 0
set @nb_rows = 0

-- regarde si il y a déjà des réponses pour cette form
select @nb_rows_responses = COUNT(*) from OpinionOrderFormsResponses WHERE  form_id = @form_id
IF @nb_rows_responses > 0
BEGIN
	select -2
END



/*
SELECT @form_id =form_id, @nb_rows = COUNT(*) 
FROM OpinionOrderForms
WHERE  order_id = @order_id and structure_id =@structure_id
GROUP BY form_id
*/


DECLARE @SQL nvarchar(max) 
set @SQL = 'SELECT  @form_id= form_id, @nb_rows = COUNT(*) FROM OpinionOrderForms WHERE structure_id = '+CONVERT(VARCHAR(50),@pStructureId)

if @pOrderId <> '' or @pOrderId > 0
begin 
	
	set @SQL = @SQL + ' and order_id = '+CONVERT(VARCHAR(50),@pOrderId)
end

if @pBasketId <> '' or @pBasketId > 0
begin 
	
	set @SQL = @SQL + ' and basket_id = '+CONVERT(VARCHAR(50),@pBasketId)
end


set @SQL = @SQL + ' GROUP BY form_id'


EXEC sp_executesql @SQL, N'@form_id int out, @nb_rows int out', @form_id out, @nb_rows out


IF @nb_rows =1 and @nb_rows_responses = 0
BEGIN
	UPDATE OpinionOrderForms set comment = @comment, date_response = GETDATE() where form_id = @form_id
	select @form_id
END

if  @nb_rows <> 1 and @nb_rows_responses = 0
	begin
		select -1
	end




/*


declare @evals_response_form_id int

select @evals_response_form_id = id 
from evals_response_form
where command_id = @order_id


if @evals_response_form_id IS NULL 
	begin
		insert into evals_response_form (command_id, response_date, comment)
		values (@order_id, getdate(), @comment)

		SELECT  CAST(scope_identity() AS int)
	end
else 
	select -1
	*/