
/*
DECLARE @pLangCode VARCHAR(2)
set @pLangCode = 'fr'
*/


DECLARE @langue_id int
SELECT @langue_id = langue_id FROM langue WHERE langue_code = @pLangCode

IF @langue_id IS NULL
	SET @langue_id = 0



SELECT distinct pf.Produit_Famille_ID, pf.Produit_Famille_Nom, pf.Produit_Famille_Code, pf.<PERSON>uleur,
	 pf.<PERSON>,
	pf.Ordre_Affichage,

	psf.Produit_Sous_Famille_ID, Produit_Sous_Famille_Nom, psf.Produit_Sous_Famille_Code,
	psf.Couleur, psf.Ordre_Affichage, psf.Masquer ,

	p.produit_id, tp.produit_nom, p.produit_code,  p.produit_descrip, 
	p.groupe_id,  p.pref_affichage
	FROM Produit_Lien_Sous_Famille plsf
	JOIN Produit_Famille pf on plsf.Produit_Famille_ID = pf.Produit_Famille_ID
	JOIN Produit_Sous_Famille psf on psf.Produit_Sous_Famille_ID = plsf.Produit_Sous_Famille_ID
	JOIN produit p on p.produit_id = plsf.Produit_id
	inner join produit_stock ps ON ps.produit_id = p.produit_id
	inner join produit_internet p_i ON p_i.produit_id = p.produit_id
	LEFT OUTER JOIN traduction_produit tp on  tp.produit_id = p.produit_id and tp.langue_id = @langue_id
	WHERE p.groupe_id in (99) and p.produit_anu <> 99
	AND psf.Masquer = 0 and pf.Masquer = 0   
	and p.internet = 1
	and p_i.acces_autonome = 1 and ps.manifestation_id = 0 and ps.seance_id = 0
UNION 
		select 
	case 
		when p.groupe_id > 0  then -(p.groupe_id) 
		else -1 end as Produit_Famille_ID,
	case 
		when p.groupe_id > 0  then concat('noname_group', (p.groupe_id))
		else 'noname' end as Produit_Famille_Nom,
	case 
		when p.groupe_id > 0  then concat('noname_group', (p.groupe_id))
		else 'noname' end as Produit_Famille_Code,
	 '' as Couleur,
	0 as Masquer, 0 as Ordre_Affichage,
	case 
		when p.groupe_id > 0  then -(p.groupe_id) 
		else -1 end as Produit_Sous_Famille_ID,
	case 
		when p.groupe_id > 0  then concat('noname_group', (p.groupe_id))
		else 'noname' end as Produit_Sous_Famille_Nom,
	case 
		when p.groupe_id > 0  then concat('noname_group', (p.groupe_id))
		else 'noname' end as Produit_Sous_Famille_Code,
	'' as Couleur, 0 as Ordre_Affichage, 0 as Masquer,

	p.produit_id, tp.produit_nom, p.produit_code,  p.produit_descrip, 
	p.groupe_id,  p.pref_affichage
	FROM produit p 
	LEFT OUTER JOIN traduction_produit tp on  tp.produit_id = p.produit_id and tp.langue_id = @langue_id
	inner join produit_stock ps ON ps.produit_id = p.produit_id
	inner join produit_internet p_i ON p_i.produit_id = p.produit_id
	WHERE p.groupe_id not in (1, 6, 7, 12) and p.produit_anu <> 99
	AND p.produit_id not in (select produit_id from Produit_Lien_Sous_Famille)
	and p.internet = 1
	and p_i.acces_autonome = 1 and ps.manifestation_id = 0 and ps.seance_id = 0
 order by p.pref_affichage
 
