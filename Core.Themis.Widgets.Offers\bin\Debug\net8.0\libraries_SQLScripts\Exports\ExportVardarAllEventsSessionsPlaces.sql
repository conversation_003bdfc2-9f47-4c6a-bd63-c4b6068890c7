﻿SELECT m.manifestation_id as event_id, convert(int, seance_Id) as session_id,
	m.manifestation_nom as event_name, s.seance_date_deb as session_date,
	l.lieu_id as place_id, l.lieu_nom as place_name, l.lieu_rue1 as place_street1,
	l.lieu_rue2 as place_street2,
	l.lieu_rue3 as place_street3,
	l.lieu_rue4 as place_street4,
	l.lieu_cp as place_zip,
	l.lieu_ville as place_city
	  FROM seance s
  INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
  INNER JOIN lieu l on l.lieu_id = s.lieu_id
	WHERE seance_date_deb > @pdateFrom and seance_date_deb < @pdateTo
	ORDER BY l.lieu_nom, s.seance_date_deb