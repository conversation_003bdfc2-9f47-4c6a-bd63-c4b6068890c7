 /* 
declare @psessionId int = 0
 */

declare @tableVts table (
	vts_id int, 
	tarif_logique_id int, 
	seance_id int, 
	categ_id int, 
	type_tarif_id int, 
	vts_grille1 decimal(18,10), 
	vts_grille2 decimal(18,10), 
	vts_grille3 decimal(18,10), 
	vts_grille4 decimal(18,10),
	vts_grille5 decimal(18,10),
	vts_grille6 decimal(18,10), 
	vts_grille7 decimal(18,10), 
	vts_grille8 decimal(18,10), 
	vts_grille9 decimal(18,10), 
	vts_grille10 decimal(18,10), 
	vts_valeur decimal(18,10),
	vts_v int,
	operateur_id int
)

if (@psessionId) = 0 
BEGIN 
insert into @tableVts (  vts_id, tarif_logique_id, seance_id, categ_id, type_tarif_id, 
	vts_grille1, vts_grille2, vts_grille3, vts_grille4,vts_grille5,vts_grille6, vts_grille7, vts_grille8, vts_grille9, vts_grille10, vts_valeur, vts_v, operateur_id)
	SELECT   vts_id, tarif_logique_id, seance_id, categ_id, type_tarif_id, 
	vts_grille1, vts_grille2, vts_grille3, vts_grille4,vts_grille5,vts_grille6, vts_grille7, vts_grille8, vts_grille9, vts_grille10, vts_valeur, vts_v, operateur_id

	
	
	FROM valeur_tarif_stock[EventID] vts 
	
END
ELSE
BEGIN
	insert into @tableVts (  vts_id, tarif_logique_id, seance_id, categ_id, type_tarif_id, 
	vts_grille1, vts_grille2, vts_grille3, vts_grille4,vts_grille5,vts_grille6, vts_grille7, vts_grille8, vts_grille9, vts_grille10, vts_valeur, vts_v, operateur_id)
	SELECT  vts_id, tarif_logique_id, seance_id, categ_id, type_tarif_id, 
	vts_grille1, vts_grille2, vts_grille3, vts_grille4,vts_grille5,vts_grille6, vts_grille7, vts_grille8, vts_grille9, vts_grille10, vts_valeur, vts_v, operateur_id 

	
	FROM valeur_tarif_stock[EventID] vts 
	WHERE seance_id = @psessionId
END
select
	structure_id,  vts_id, tarif_logique_id, seance_id, categ_id, type_tarif_id, 
	convert(int, vts_grille1 *100) as vts_grille1, 
	convert(int, vts_grille2*100) as vts_grille2, 
	convert(int, vts_grille3*100) as vts_grille3, 
	convert(int, vts_grille4*100) as vts_grille4,
	convert(int, vts_grille5*100) as vts_grille5,
	convert(int, vts_grille6*100) as vts_grille6,
	convert(int, vts_grille7*100) as vts_grille7,
	convert(int, vts_grille8*100) as vts_grille8,
	convert(int, vts_grille9*100) as vts_grille9,
	convert(int, vts_grille10*100) as vts_grille10,
	convert(int, vts_valeur*100) as vts_valeur,
	montant_payer = convert(int, (vts.vts_grille1+vts.vts_grille2 +
					 case when modecol4='REMISE' then - vts.vts_grille4 
                     when modecol4='TAXE' then vts.vts_grille4
                     else 0 END +
                     case  when modecol5='REMISE' then - vts.vts_grille5
                     when modecol5='TAXE'  then vts.vts_grille5
                     else 0 END +
                     case  when modecol6='REMISE' then - vts.vts_grille6
                     when modecol6='TAXE'  then vts.vts_grille6
                     else 0 END +
                     case  when modecol7='REMISE' then - vts.vts_grille7
                     when modecol7='TAXE'  then vts.vts_grille7
                     else 0 END +
                     case  when modecol8='REMISE' then - vts.vts_grille8
                     when modecol8='TAXE'  then vts.vts_grille8
                     else 0 END +
                     case  when modecol9='REMISE' then - vts.vts_grille9
                     when modecol9='TAXE'  then vts.vts_grille9
                     else 0 END +
                     case  when modecol10='REMISE' then - vts.vts_grille10
                     when modecol10='TAXE' then vts.vts_grille10
                     else 0 END)*100),	 
	 vts_v, operateur_id

from @tableVts vts , structure s

