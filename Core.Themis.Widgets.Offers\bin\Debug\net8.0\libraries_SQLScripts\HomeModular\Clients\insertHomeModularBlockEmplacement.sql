﻿
/*
DECLARE @pBlockEmplacementId int
DECLARE @pEmplacementId int
DECLARE @pBlockTypeId int
DECLARE @pBlockOrder int
DECLARE @pEmplacementGroupId int


set @pEmplacementId = 4
set @pBlockTypeId = 2
set @pBlockOrder = 1
set @pEmplacementGroupId = 2

set @pBlockEmplacementId = 1
*/

--si le blockEmplacement est 0, on ajoute
if(@pBlockEmplacementId = 0)
BEGIN
	insert into HomeModular_BlockEmplacement values(@pEmplacementId, @pBlockTypeId, @pBlockOrder, @pEmplacementGroupId)
	select convert(int, SCOPE_IDENTITY())
END

--Si un blockEmplacement existe, on met à jour
if(@pBlockEmplacementId > 0)
BEGIN
	update HomeModular_BlockEmplacement set Emplacement_ID=@pEmplacementId, BlockType_ID=@pBlockTypeId, Block_Order=@pBlockOrder, EmplacementGroup_ID=@pEmplacementGroupId where BlockEmplacement_ID = @pBlockEmplacementId
	select @pBlockEmplacementId
END

