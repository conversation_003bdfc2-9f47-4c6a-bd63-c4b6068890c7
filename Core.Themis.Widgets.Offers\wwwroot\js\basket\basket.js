﻿$(document).ready(function () {
    formatDevise()
    initChangeModalAndCollapse()
    sendIframeSize()
    //bouton confirm panier
    $('#confirmBasket').off('click').on('click', function () {
        if (!$(this).prop("disabled")) {
            $('#allAlertsBasket .alert').hide()
            sendIframeSize()
            $("#backToLastEvent").addClass("disabled").prop("disabled", true)
            $("#backToEventsList").addClass("disabled").prop("disabled", true)
            loadingButtonBootstrapOn(this)
            insertConsumerBP(function () { insertSelectablesProducts(function () { insertObtainingMode() }) })
        }
    })
    var configSpinner = {
        decrementButton: "<strong>-</strong>",
        incrementButton: "<strong>+</strong>",
        groupClass: "w-auto customSpinner customSpinnerLeftButton d-inline-flex",
        buttonsClass: "btn-secondary",
        buttonsWidth: "2.5rem",
        textAlign: "right",
        autoDelay: 500,
        autoInterval: undefined,
        locale: langCode,
        template: '<div class="input-group ${groupClass}">' +
            '<button style="min-width: ${buttonsWidth}" class="btn btn-decrement ${buttonsClass}" type="button">${decrementButton}</button>' +
            '<button style="min-width: ${buttonsWidth}" class="btn btn-increment ${buttonsClass}" type="button">${incrementButton}</button>' +
            '<input type="text" inputmode="decimal" style="text-align: ${textAlign}" class=""/>' +
            '</div>'
    }
    $('.inptProductVariableAmount').attr('data-decimals',2)
    $('.inptProductVariableAmount').inputSpinner(configSpinner)
    $('.btn-decrement, btn-increment').on('click', function (e) {
        if ($(this).prop('disabled')) {
            e.preventDefault()
            e.stopPropagation()
        }
    })
    $('.inptProductVariableAmountWrapper, .inptProductVariableUnitWrapper').off('focusin').on('focusin', function () {
        $(this).closest('.oneProduct').addClass('focusin')
    })
    $('.inptProductVariableAmountWrapper, .inptProductVariableUnitWrapper').off('focusout').on('focusout', function () {
        $(this).closest('.oneProduct').removeClass('focusin')
    })

    if ($('.inptProductVariableUnit').data('typemontant') == 2 && $('.inptProductVariableUnit').data('min') > 0) {

        var originalInput = $('.inptProductVariableUnit');

        calculProduitMontantVariable(originalInput);
    }





    //produits unités variables
    $('.inptProductVariableUnit').off('change').on('change', function (e) {
        var originalInput = $(this)

        calculProduitMontantVariable(originalInput);

        //var lastVal = parseInt($(originalInput).attr('data-lastval')) || 0;
        //var valMin = parseInt($(originalInput).attr('data-realmin'))
        //var valMax = parseInt($(originalInput).attr('max'))
        //var val = parseInt($(originalInput).val())
        
        ////si la valeur est supérieur a 0, alors le produit est actif
        //if ($(originalInput).val() > 0) {
        //    $(originalInput).closest('.oneProduct').addClass('active')

        //} else {
        //    $(originalInput).closest('.oneProduct').removeClass('active')

        //}
        ////
        //$(originalInput).attr('data-lastval', parseInt($(originalInput).val()))
        ////
        //var value = $(originalInput).val();
        //var unitprice = parseInt($(originalInput).attr('data-unitpricettccents'));
        //var tva = parseFloat($(originalInput).attr('data-tvataux').replace(',', '.'));

        //if ($(this).data('typemontant') == 2) {
        //    let unitpriceInDB = getProduitCalculeParProcedureStockee(originalInput);
        //    if (unitpriceInDB != undefined && unitpriceInDB > 0) {
        //        unitprice = unitpriceInDB;
        //    }
        //}

        //var totalpricettc = value * unitprice;
        //var totalpriceht = (totalpricettc / (1 + (tva / 100)))
        //$(originalInput).attr('data-tvaamount', (totalpricettc - totalpriceht))
        //$(originalInput).closest('.oneProduct').find('.priceTotalWrapper .priceTotal').attr('data-pricetoformat', totalpricettc);
        //$(originalInput).closest('.oneProduct').find('.priceTotalWrapper .priceTotal').html(SetDeviseCode(totalpricettc));

        if (typeof customPackage === "function") {
            customPackage()
        }
        getModesObtentionsView()
        blockCollapseProductsEventSession()
    })
    //produits montant variables
    //premier passage pour intialiser les + et -
    $.each($('.inptProductVariableAmount'), function (i,k) {
        var originalInput = $(k).closest('.inptProductVariableAmountWrapper').find('.inptProductVariableAmount[type="number"]')
        var valMin = parseFloat($(originalInput).attr('data-realmin'))
        var valMax = parseFloat($(originalInput).attr('max'))
        var val = parseFloat($(originalInput).val())
        if ($(originalInput).val() >= valMax) {
            $(originalInput).next('.customSpinner').find('.btn-increment').addClass('disabled').prop('disabled', true)
        }

        if ($(originalInput).val() <= 0) {
            $(originalInput).next('.customSpinner').find('.btn-decrement').addClass('disabled').prop('disabled', true)
        }
    })
    //on change
    $('.inptProductVariableAmount').off('change').on('change', function (e) {
        var originalInput = $(this).closest('.inptProductVariableAmountWrapper').find('.inptProductVariableAmount[type="number"]')
        var lastVal = parseFloat($(originalInput).attr('data-lastval')) || (0).toFixed(2);
        var valMin = parseFloat($(originalInput).attr('data-realmin'))
        var valMax = parseFloat($(originalInput).attr('max'))
        var val = parseFloat($(originalInput).val())

        $(originalInput).next('.customSpinner').find('.btn-increment').removeClass('disabled').prop('disabled', false)
        $(originalInput).next('.customSpinner').find('.btn-decrement').removeClass('disabled').prop('disabled', false)
        //++
        if (val > lastVal) {
            //si la valeur est inférieur au min, alors val == min
            if (val < valMin) {
                $(originalInput).val(valMin)
            }
            //si la valeur est supérieur au max, alors val == max
            if (val > valMax) {
                $(originalInput).val(valMax)
            }
        }
        //--
        if (val < lastVal) {
            //si la valeur est inférieur au min, alors val == 0
            if (val < valMin) {
                $(originalInput).val(0)
            }
        }
        //si la valeur est supérieur a 0, alors le produit est actif
        if (parseFloat($(originalInput).val().replace(",", ".")) > 0) {
            $(originalInput).closest('.oneProduct').addClass('active')
        } else {
            $(originalInput).closest('.oneProduct').removeClass('active')
        }
        //
        $(originalInput).attr('data-lastval', parseFloat($(originalInput).val()).toFixed(2))
        $(originalInput).val(parseFloat($(originalInput).val()).toFixed(2))

        if ($(originalInput).val() >= valMax) {
            $(originalInput).next('.customSpinner').find('.btn-increment').addClass('disabled').prop('disabled', true)
        }

        if ($(originalInput).val() <= 0) {
            $(originalInput).next('.customSpinner').find('.btn-decrement').addClass('disabled').prop('disabled', true)
        }
        //
        var value = 1;
        var unitprice = parseInt($(originalInput).val() * 100);
        var tva = parseFloat($(originalInput).attr('data-tvataux').replace(',', '.'));

        var totalpricettc = value * unitprice;
        var totalpriceht = (totalpricettc / (1 + (tva / 100)))
        $(originalInput).attr('data-tvaamount', (totalpricettc - totalpriceht))
        $(originalInput).closest('.oneProduct').find('.priceTotalWrapper .priceTotal').attr('data-pricetoformat', totalpricettc);
        //$(this).closest('.oneProduct').find('.priceTotalWrapper .priceTotal').html(SetDeviseCode(totalpricettc));
        getModesObtentionsView()
        blockCollapseProductsEventSession()
    })

    blockCollapseProductsEventSession()

    //cgv
    $('#seeCGV').off('click').on('click', function (e) {
        e.preventDefault()
        $("#modalCGV").modal('show', $(this))
    })

    //bouton retour dernier évènement
    $("#backToLastEvent").off('click').on('click', function (e) {
        if (!$(this).prop("disabled")) {
            var msg = {
                "action": "backtolastevent",
                "structureid": $(this).attr('data-structureid'),
                "eventid": $(this).attr('data-eventid'),
            }
            window.parent.postMessage(msg, '*')
        }
    })
    //bouton retour liste évènements
    $("#backToEventsList").off('click').on('click', function (e) {
        if (!$(this).prop("disabled")) {
            var msg = {
                "action": "backtoeventlist",
                "structureid": $(this).attr('data-structureid')
            }
            window.parent.postMessage(msg, '*')
        }
    })



    //bouton voir mon placement
    $('.btnSeeMyPlacement').off('click').on('click', function () {
        if (!$(this).prop("disabled")) {
            console.log("btnSeeMyPlacement ")
            var btn = $(this)
            loadingButtonBootstrapOn(btn)
            var eventid = parseInt($(this).attr('data-eventid'))
            var sessionid = parseInt($(this).attr('data-sessionid'))
            var zoneid = $(this).attr('data-zoneid').split(",")
            var floorid = $(this).attr('data-floorid').split(",")
            var sectionid = $(this).attr('data-sectionid').split(",")
            var placeid = parseInt($(this).attr('data-placeid'))
            var identityid = identityId

            $.ajax({
                type: "POST",
                url: sWOffersUrl + "Session/ChoiceSeatsPlanAjax/" + structureId + "/" + Basket.basketId + "/" + buyerProfilId + "/" + identityid + "/" + langCode + "/" + eventid + "/" + sessionid + "/" + placeid,
                 data: {
                    webUserId: webUserId,
                    isViewOnly: true,
                    categsId: [],
                    listgpidPlan: [],
                    token: partnerToken,
                    zoneIds: zoneid,
                    floorIds: floorid,
                    sectionIds: sectionid
                },
                success: function (data) {
                    getSeatingPlans(structureId, placeid)
                    DrawPlanSeeMyPlacement(data)
                    
                },
                error: function (a, b, c) {
                    console.log("ChoiceSeatsPlanAjax -> Error")
                },
                complete: function () {
                    loadingButtonBootstrapOff(btn)
                }
            });
        }
    })
    //ouvre la modal de login a l'arrivée si l'utilisateur n'est pas connecté
    if (identityId == 0) {
        window.parent.postMessage({
            "action": "openCustomerArea",
        }, "*")
    }
    $('#goToLogin').off('click').on('click', function (e) {
        window.parent.postMessage({
            "action": "openCustomerArea",
        }, "*")
    })

    $('#goToLoginTunnel').off('click').on('click', function (e) {
        {
            e.preventDefault()
            $("#modalLogin").modal('show', $(this))
        }
    })


    initMos()
    initDeleteSession()
    initDeleteProducts()

    if (typeof customPackage === "function") {
        customPackage()
    }
});


function calculProduitMontantVariable(originalInput) {
    var lastVal = parseInt($(originalInput).attr('data-lastval')) || 0;
    var valMin = parseInt($(originalInput).attr('data-realmin'))
    var valMax = parseInt($(originalInput).attr('max'))
    var val = parseInt($(originalInput).val())

    //si la valeur est supérieur a 0, alors le produit est actif
    if ($(originalInput).val() > 0) {
        $(originalInput).closest('.oneProduct').addClass('active')

    } else {
        $(originalInput).closest('.oneProduct').removeClass('active')

    }
    //
    $(originalInput).attr('data-lastval', parseInt($(originalInput).val()))
    //
    var value = $(originalInput).val();
    var unitprice = parseInt($(originalInput).attr('data-unitpricettccents'));
    var tva = parseFloat($(originalInput).attr('data-tvataux').replace(',', '.'));

    if ($(originalInput).data('typemontant') == 2) {
        let unitpriceInDB = getProduitCalculeParProcedureStockee(originalInput);
        if (unitpriceInDB != undefined && unitpriceInDB > 0) {
            unitprice = unitpriceInDB;
        }
    }

    var totalpricettc = value * unitprice;
    var totalpriceht = (totalpricettc / (1 + (tva / 100)))
    $(originalInput).attr('data-tvaamount', (totalpricettc - totalpriceht))
    $(originalInput).closest('.oneProduct').find('.priceTotalWrapper .priceTotal').attr('data-pricetoformat', totalpricettc);
    $(originalInput).closest('.oneProduct').find('.priceTotalWrapper .priceTotal').html(SetDeviseCode(totalpricettc));


}
function getProduitCalculeParProcedureStockee(data) {
    if ($(data).val() > 0) {
        let productId = $(data).data('productid');
        let montant = 0;
 
        let url = widgetOfferUrl + structureId + "/calculProductByStoredProcedureAjax/" + Basket.basketId + "/" + productId + "/" + identityId 
        $.ajax({
            type: "POST",
            url: url,
            async: false, 
            data: {
                token: partnerToken
            },
            success: function (data) {
                if (data != null) {
                    montant = data;
                }
            },
            error: function (a, b, c) {
                console.log("calculProductByStoredProcedureAjax -> Error")
            }
        });
        return montant;
    }
}
//bloque ou débloque le bouton de collapse pour les produits event/session
function blockCollapseProductsEventSession() {
    //produit session
    $.each($('[id^=collapseProductsSession_'), function (i, k) {
        var collapseProductsSessionSelected = false;
        $.each($(k).find('.oneProduct'), function (inpi, inpk) {
            if ($(inpk).hasClass('active')) {
                collapseProductsSessionSelected = true;
            }
        })
        if (collapseProductsSessionSelected) {
            $(k).collapse("show")
            $('.productsSessionBtn[data-target="#' + $(k).attr("id") + '"]').prop('disabled', true).addClass("disabled")
        } else {
            $('.productsSessionBtn[data-target="#' + $(k).attr("id") + '"]').prop('disabled', false).removeClass("disabled")
        }
    })
    //produit event
    $.each($('[id^=collapseProductsEvent_'), function (i, k) {
        var collapseProductsEventSelected = false;
        $.each($(k).find('.oneProduct'), function (inpi, inpk) {
            if ($(inpk).hasClass('active')) {
                collapseProductsEventSelected = true;
            }
        })
        if (collapseProductsEventSelected) {
            $(k).collapse("show")
            $('.productsEventBtn[data-target="#' + $(k).attr("id") + '"]').prop('disabled', true).addClass("disabled")
        } else {
            $('.productsEventBtn[data-target="#' + $(k).attr("id") + '"]').prop('disabled', false).removeClass("disabled")
        }
    })
    
}


// LESSREADY  se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
// (custom pour la page "Events")
function lessReady() {
    sendIframeSize()
    console.log('basket.js lessReady READY')

}
//formatage des devises
function formatDevise() {
    $.each($('[data-pricetoformat]'), function (i, k) {
        $(k).html(SetDeviseCode(parseInt($(k).attr('data-pricetoformat'))))
    })
}

//initialisation du delete session
function initDeleteSession() {
    $('.oneSession .basketDeleteSession').off('click').on('click', function () {
        var seatIds = []
        $.each($(this).closest('.oneSession').find(".productDetailsWrapper"), function (i, k) {
            seatIds.push(parseInt($(k).attr("data-seatid")))
        })
        seatIds = seatIds.filter(function (elem, index, self) {
            return index === self.indexOf(elem);
        })
        var categIds = []
        $.each($(this).closest('.oneSession').find(".productDetailsWrapper"), function (i, k) {
            categIds.push(parseInt($(k).attr("data-categid")))
        })
        categIds = categIds.filter(function (elem, index, self) {
            return index === self.indexOf(elem);
        })
        var eventId = parseInt($(this).closest('.oneSession').attr('data-eventid'))
        var sessionId = parseInt($(this).closest('.oneSession').attr('data-sessionid'))
        var url = widgetOfferUrl + structureId + "/unFlagAjax/" + Basket.basketId + "/" + eventId + "/" + sessionId + "/" + webUserId + "/" + langCode;
        $.ajax({
            type: "POST",
            url: url,
            data: {
                categsId: categIds,
                seatsId: seatIds,
                token: partnerToken
            },
            success: function (data) {
                if (data.etat == "I") {
                    var msg = {
                        "action": "backtoeventlist",
                        "structureid": numToNDigitStr(structureId, 4)
                    }
                    window.parent.postMessage(msg, '*')
                } else { 
                    var msg = {
                        "action": "reloadWidget",
                        "structureid": numToNDigitStr(structureId, 4),
                        "htmlSelector": window.htmlSelector
                    }
                    window.parent.postMessage(msg, '*')
                    location.reload();
                }

            },
            error: function (a, b, c) {
                console.log("unflagajax -> Error")
            }
        });
    })
}

//initialisation du delete session
function initDeleteProducts() {
    $('.oneProduct .basketDeleteProduct').off('click').on('click', function () {
        var basketlineId = parseInt($(this).closest(".oneProduct").attr("data-basketlineid"))
        var url = widgetOfferUrl + "basket/" + structureId + "/deleteProductAjax/" + Basket.basketId + "/" + webUserId + "/" + langCode;
        $.ajax({
            type: "DELETE",
            url: url,
            data: {
                basketProductId: basketlineId,
                token: partnerToken
            },
            success: function (data) {

                if (data.etat == "I") {
                    var msg = {
                        "action": "backtoeventlist",
                        "structureid": numToNDigitStr(structureId, 4)
                    }
                    window.parent.postMessage(msg, '*')
                } else {
                    var msg = {
                        "action": "reloadWidget",
                        "structureid": numToNDigitStr(structureId, 4),
                        "htmlSelector": window.htmlSelector
                    }
                    window.parent.postMessage(msg, '*')
                    location.reload();
                }
            },
            error: function (a, b, c) {
                console.log("deleteProductAjax -> Error")
            }
        });
    })
}


//initialisation du changement des MOs
function initMos() {

    $('#basketFeesWrapper .oneSummaryLine[data-moid]').remove()
    $('#ddlObtentionAll, #ddlObtentionEvents, #ddlObtentionProducts').off('change').on('change', function () {
        var motype = $(this).attr('data-selectmotype')
        $('#basketFeesWrapper .oneSummaryLine[data-moid][data-selectmotype="' + motype + '"]').remove()
        if ($(this).val() != 0) {
            var templateMo = $('#hidden-template-mo-oneline').html();
            var thisMoId = $(this).val()
            var thisMoName = $(this).find('option:selected').html()
            var thisMoPrice = parseInt($(this).find('option:selected').attr('data-amount'))
            var thisMoTVATaux = $(this).find('option:selected').attr('data-tvataux')
            var thisMoTVAAmount = $(this).find('option:selected').attr('data-tvaamount')
            $('#basketFeesWrapper').prepend(templateMo
                .replaceAll("[moId]", thisMoId)
                .replaceAll("[moType]", motype)
                .replaceAll("[moName]", thisMoName)
                .replaceAll("[moPriceCents]", thisMoPrice)
                .replaceAll("[moPrice]", SetDeviseCode(thisMoPrice))
                .replaceAll("[moTVAAmount]", thisMoTVAAmount)
                .replaceAll("[moTVATaux]", thisMoTVATaux)
            )


        }
        sessionStorage.setItem("widget_basket_mo_" + Basket.basketId, JSON.stringify(isMoSelected(true)));
        checkfees()
        calculBasket()

    })

    //on parcours les select Mos, si celui la ne contient qu'une seule option (hors option = 0), on la selectionne
    $.each($('#ddlObtentionAll, #ddlObtentionEvents, #ddlObtentionProducts'), function (i, k) {
        if ($(k).find('option').not('[value = "0"]').length == 1) {
            $(k).find('option').not('[value = "0"]').first().prop('selected', true)
        }
        $(k).trigger('change')
    })

    // on parcours tous les sesison storage
    if (sessionStorage.length > 0) {
        var sessionStorageToRemove = [];
        for (var i = 0, len = sessionStorage.length; i < len; i++) {
            var key = sessionStorage.key(i);
            //on stock tous les anciens panier
            if (key.startsWith("widget_basket_mo_") && key != "widget_basket_mo_" + Basket.basketId) {
                sessionStorageToRemove.push(key)
            }
            //si on trouve un sessionStorage pour ce panier "widget_basket_mo_" + Basket.basketId, on re-attribut ses valeurs au MO
            if (key == "widget_basket_mo_" + Basket.basketId) {
                ssvalue = JSON.parse(sessionStorage.getItem("widget_basket_mo_" + Basket.basketId))
                //si mo communs
                if ($('#ddlObtentionAll').length > 0) {
                    $('#ddlObtentionAll').val(ssvalue.MO_ENTREES).trigger('change')
                }
                //si mo doubles
                if ($('#ddlObtentionEvents').length > 0 && $('#ddlObtentionProducts').length > 0) {
                    $('#ddlObtentionEvents').val(ssvalue.MO_ENTREES).trigger('change')
                    $('#ddlObtentionProducts').val(ssvalue.MO_PRODUCTS).trigger('change')
                }
            }
        }
        //on supprime tous les anciens paniers
        if (sessionStorageToRemove.length > 0) {
            $.each(sessionStorageToRemove, function (i, k) {
                sessionStorage.removeItem(k);
            })
        }
    }
    
}

function checkfees() {
    if ($('#basketFeesWrapper .oneSummaryLine').length > 0) {
        $('#basketFeesWrapper').show()
    } else {
        $('#basketFeesWrapper').hide()
    }
    sendIframeSize()
}

//insertion du nom de consommateur pour les profil acheteurs
function insertConsumerBP(callback) {

    if ($('#inputConsommateurPA').length > 0) {
        if ($('#inputConsommateurPA').val().trim().replace(" ", "") == "") {
            // message d'erreur, renseigner le nom du consommateur
            $('#alert-consumerPA-empty').show()
            loadingButtonBootstrapOff('#confirmBasket')
            $("#backToLastEvent").removeClass("disabled").prop("disabled", false)
            $("#backToEventsList").removeClass("disabled").prop("disabled", false)
            sendIframeSize()
        }
        else {
            var url = widgetOfferUrl + structureId + "/insertConsumerBPAjax/" + Basket.basketId;
            $.ajax({
                type: "POST",
                url: url,
                data: {
                    ConsommName: $('#inputConsommateurPA').val(),
                    token: partnerToken
                },
                success: function (data) {
                    if (data != false && typeof callback === 'function') {
                        callback();
                    }
                },
                error: function (a, b, c) {
                    console.log("insertConsumerBPAjax -> Error")
                    jQuery("#insertConsumerBPAjax").html(a.responseText);
                }
            });
        }
    } else {
        if (typeof callback === 'function') {
            callback();
        }
    }


}

//charge la partial view des modes d'obtention
function getModesObtentionsView() {

    var alistGps = [];
    var alistProductsId = []
    var alistProductsCADHId = []
    //on parcours tous les évenements pour récuperer les GPIDS
    if (Basket.listEventsUnitSales.length > 0) {
        $.each(Basket.listEventsUnitSales, function (leventsi, leventsk) {
            if (leventsk.listSessions.length > 0) {
                $.each(leventsk.listSessions, function (lsessionsi, lsessionsk) {
                    if (lsessionsk.listZones.length > 0) {
                        $.each(lsessionsk.listZones, function (lzonesi, lzonesk) {
                            if (lzonesk.listFloors.length > 0) {
                                $.each(lzonesk.listFloors, function (lfloorsi, lfloorsk) {
                                    if (lfloorsk.listSections.length > 0) {
                                        $.each(lfloorsk.listSections, function (lsectionsi, lsectionsk) {
                                            if (lsectionsk.listCategories.length > 0) {
                                                $.each(lsectionsk.listCategories, function (lcategoriesi, lcategoriesk) {
                                                    if (lcategoriesk.listPrices.length > 0) {
                                                        $.each(lcategoriesk.listPrices, function (lpricesi, lpricesk) {
                                                            if (lpricesk.listGestionPlace.length > 0) {
                                                                $.each(lpricesk.listGestionPlace, function (lgpi, lgpk) {
                                                                    alistGps.push(lgpk.gestionPlaceId)
                                                                })
                                                            }
                                                        })
                                                    }
                                                })
                                            }
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            }
        })
    }
    //on parcours tous les produits purs
    if (Basket.listProduitsWT.length > 0) {
        $.each(Basket.listProduitsWT, function (lproductsi, lproductsk) {
            alistProductsId.push(lproductsk.productId)
        })
    }
    //on parcours tous les produits globaux
    $.each($('.inptProductVariableUnit, .inptProductVariableAmount[type="number"]'), function (i, k) {
        if (parseInt($(k).val()) > 0) {
            alistProductsId.push(parseInt($(k).attr('data-productid')))
        }
    })
    //on parcours tous les produits adhésions
    if (Basket.listProductCartesAdhesion.length > 0) {
        $.each(Basket.listProductCartesAdhesion, function (lproductsi, lproductsk) {
            alistProductsCADHId.push(lproductsk.productId)
        })
    }

    $.ajax({
        type: "POST",
        url: widgetOfferUrl + structureId + "/getModesObtentionsViewAjax",
        data: {
            langCode: langCode,
            identiteId: Basket.identityId,
            basketId: Basket.basketId,
            buyerProfilId: buyerProfilId,
            listGps: alistGps,
            listProductsId: alistProductsId,
            listProductsCADHId: alistProductsCADHId
        },
        success: function (data) {
            $('#ModesObtentionsWrapper').html(data)
            initMos()
        },
        error: function (a, b, c) {
            console.log("getModesObtentionsViewAjax -> Error")
        },
        complete: function () {
            sendIframeSize()
        }
    });
}

//insertion des produits selectionnables (unité variable, montant variable)
function insertSelectablesProducts(callback) {
    //s'il y a des produits à unité variable ou à montant variable
    if ($('.inptProductVariableUnit').length > 0 || $('.inptProductVariableAmount[type="number"]').length > 0) {
        var listProducts = []
        //ajoute les produits à unité variable
        $.each($('.inptProductVariableUnit'), function (i, k) {
            var myObjTmp = {}
            myObjTmp.SessionId = 0
            myObjTmp.EventId = 0
            //si produit lié à l'event ou session
            if ($(k).closest('.oneEvent').length > 0) {
                myObjTmp.EventId = parseInt($(k).closest('.oneEvent').attr('data-eventid'))
            }
            //si produit lié à la session
            if ($(k).closest('.oneSession').length > 0) {
                myObjTmp.SessionId = parseInt($(k).closest('.oneSession').attr('data-sessionid'))
            }
            myObjTmp.ProductId = parseInt($(k).attr('data-productid'))
            myObjTmp.Count = parseInt($(k).val())
            myObjTmp.Amount = 0

            if ($(k).data('typemontant') == 2) {

                myObjTmp.Amount = $(k).data('unitpricettccents')
            }

            myObjTmp.TypeMontant = $(k).data('typemontant')
            listProducts.push(myObjTmp)
        })

        //ajoute les produits à montant variable
        $.each($('.inptProductVariableAmount[type="number"]'), function (i, k) {
            var myObjTmp = {}
            myObjTmp.SessionId = 0
            myObjTmp.EventId = 0
            //si produit lié à l'event ou session
            if ($(k).closest('.oneEvent').length > 0) {
                myObjTmp.EventId = parseInt($(k).closest('.oneEvent').attr('data-eventid'))
            }
            //si produit lié à la session
            if ($(k).closest('.oneSession').length > 0) {
                myObjTmp.SessionId = parseInt($(k).closest('.oneSession').attr('data-sessionid'))
            }
            myObjTmp.ProductId = parseInt($(k).attr('data-productid'))
            myObjTmp.Count = 1
            myObjTmp.Amount = parseInt($(k).attr('data-pricetoformat'))
            listProducts.push(myObjTmp)
        })
        console.log(listProducts)

        $.ajax({
            type: "POST",
            url: widgetOfferUrl + structureId + "/Product/AddBasketAjax/basket/" + Basket.identityId + "/" + webUserId + "/" + buyerProfilId + "/" + langCode,
            data: {
                listProducts: listProducts,
                token: partnerToken
            },
            success: function (data) {
                if (typeof callback === 'function') {
                    callback();
                } else {
                    loadingButtonBootstrapOff('#confirmBasket')
                    $("#backToLastEvent").removeClass("disabled").prop("disabled", false)
                    $("#backToEventsList").removeClass("disabled").prop("disabled", false)
                }
            },
            error: function (a, b, c) {
                console.log("insertSelectablesProducts -> Error")
                loadingButtonBootstrapOff('#confirmBasket')
                $("#backToLastEvent").removeClass("disabled").prop("disabled", false)
                $("#backToEventsList").removeClass("disabled").prop("disabled", false)
            },
            complete: function () {
                sendIframeSize()
            }
        });
    } else {
        if (typeof callback === 'function') {
            callback();
        }
    }
}

//insertion des modes d'obtention en bdd
function insertObtainingMode() {
    if (Basket != undefined && Basket.basketId != 0) {
        var MosSelected = isMoSelected()
        if (!$.isEmptyObject(MosSelected) && isCGVChecked()) {
            var url = widgetOfferUrl + structureId + "/insertObtainingModeAjax/" + Basket.basketId;
            $.ajax({
                type: "POST",
                url: url,
                data: {
                    langCode: langCode,
                    identiteId: Basket.identityId,
                    obtainingMode: MosSelected,
                    token: partnerToken
                },
                success: function (data) {
                    var msg = {
                        "action": "urltogo",
                        "url": data
                    }
                    window.parent.postMessage(msg, '*')
                },
                error: function (a, b, c) {
                    console.log("insertObtainingMode -> Error")
                    jQuery("#wdgInsertPlanSalle").html(a.responseText);
                },
                complete: function () {
                    loadingButtonBootstrapOff('#confirmBasket')
                    $("#backToLastEvent").removeClass("disabled").prop("disabled", false)
                    $("#backToEventsList").removeClass("disabled").prop("disabled", false)
                    sendIframeSize()
                }
            });
        } else {

            if ($.isEmptyObject(MosSelected)) {
                $('#alert-mo-empty').show()
            }
            if (!isCGVChecked()) {
                $('#alert-cgv-empty').show()
            }
            loadingButtonBootstrapOff('#confirmBasket')
            $("#backToLastEvent").removeClass("disabled").prop("disabled", false)
            $("#backToEventsList").removeClass("disabled").prop("disabled", false)
            sendIframeSize()
        }
    }
}
//verifie que les CGV sont choché
function isCGVChecked() {
    var formIsOk = true
    $('#CGVExtraOptinWrapper').addClass('was-validated')
    if ($('#checkboxCGV').is(':checked')) {
        $("#checkboxCGV")[0].setCustomValidity('');
    } else {
        $("#checkboxCGV")[0].setCustomValidity('error');
        formIsOk = false
    }

    if (formIsOk) {
        return true
    } else {
        return false
    }
}
//verifie qu'un mode d'obtention est sélectionné
function isMoSelected(zeroValues) {
    //zeroValues renvoie l'objet rempli même lorsqu'un mo est a 0
    var zeroValues = (zeroValues != undefined) ? zeroValues : false
    if (productsMOCommons.length > 0 && productsMOCommons[0].productCode != "ERROR") {
        var moall = parseInt($('#ddlObtentionAll').val())

        if (moall != 0) {
            return { 'MO_ENTREES': moall, 'MO_PRODUCTS': moall }
        } else if (zeroValues) {
            return { 'MO_ENTREES': 0, 'MO_PRODUCTS': 0 }
        } else {
            return {}
        }
    } else if (productsMO.length > 0 && productsMOProducts.length > 0) {
        var moevents = parseInt($('#ddlObtentionEvents').val())
        var moproducts = parseInt($('#ddlObtentionProducts').val())
        if (moevents != 0 && moproducts != 0) {
            return { 'MO_ENTREES': moevents, 'MO_PRODUCTS': moproducts }
        } else if (zeroValues) {
            return { 'MO_ENTREES': moevents, 'MO_PRODUCTS': moproducts }
        } else {
            return {}
        }
    } else {
        return {}
    }

}

//calcule le montant total TTC du basket + TVA
function calculBasket() {

    var totalBasketAmountTTC = 0;
    var subTotalEventsProduct = 0;
    var subTotalMoreProduct = 0;
    var subTotalFeesAmountTTC = 0;
    var LstTVA = [];
    //var totalBasketAmountHT = 0
    //on parcours les events

    $.each($('#summaryEventsProducts .allAbos'), function (i, k) {
        //totalBasketAmountHT += parseInt($(k).find('.priceTotalHT span[data-pricetoformat]').attr('data-pricetoformat'))
        totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        subTotalEventsProduct += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        //LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
    })

    $.each($('#summaryEventsProducts .oneSession'), function (i, k) {
        //totalBasketAmountHT += parseInt($(k).find('.priceTotalHT span[data-pricetoformat]').attr('data-pricetoformat'))
        totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        subTotalEventsProduct += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
    })
    //on parcours les produits
    $.each($('#summaryEventsProducts .oneProduct'), function (i, k) {
        //si le produit est un produit type : unité variable et que son unité est supérieur à 0
        //ou si le produit est un produit classique (produit autonome, produit adhesion etc..)
        //if (($(k).find('.priceTotal').attr('data-producttype') != undefined && $(k).find('.priceTotal').attr('data-producttype') == 'productVariableUnit' && parseInt($(k).find('.inptProductVariableUnit').val()) > 0) || $(k).find('.priceTotal').attr('data-producttype') == undefined) {
        //totalBasketAmountHT += parseInt($(k).find('.priceTotalHT span[data-pricetoformat]').attr('data-pricetoformat'))
        totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        subTotalEventsProduct += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
        //}
        //si leproduit est un produit type : montant variable
        /*if ($(k).find('.priceTotal').attr('data-producttype') != undefined && $(k).find('.priceTotal').attr('data-producttype') == 'productVariableAmount' && parseInt($(k).find('.inptProductVariableAmount').val()) > 0) {
            totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
            subTotalEventsProduct += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
            LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
        }*/

    })
    //on parcours les produits supplémentaire
    $.each($('#summaryMoreProducts .oneProduct'), function (i, k) {
        //si le produit est un produit type : unité variable et que son unité est supérieur à 0
        //ou si le produit est un produit classique (produit autonome, produit adhesion etc..)
        //if (($(k).find('.priceTotal').attr('data-producttype') != undefined && $(k).find('.priceTotal').attr('data-producttype') == 'productVariableUnit' && parseInt($(k).find('.inptProductVariableUnit').val()) > 0) || $(k).find('.priceTotal').attr('data-producttype') == undefined) {
        //totalBasketAmountHT += parseInt($(k).find('.priceTotalHT span[data-pricetoformat]').attr('data-pricetoformat'))
        totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        subTotalMoreProduct += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
        //}
        //si leproduit est un produit type : montant variable
        /*if ($(k).find('.priceTotal').attr('data-producttype') != undefined && $(k).find('.priceTotal').attr('data-producttype') == 'productVariableAmount' && parseInt($(k).find('.inptProductVariableAmount').val()) > 0) {
            totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
            subTotalMoreProduct += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
            LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
        }*/


    })
    //on parcours les frais au paier
    $.each($('.oneFees'), function (i, k) {
        //totalBasketAmountHT += parseInt($(k).find('.priceTotalHT span[data-pricetoformat]').attr('data-pricetoformat'))
        totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        subTotalFeesAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
    })
    //on parcours les modes d'obtentions sélectionné
    $.each($('.oneMo'), function (i, k) {
        //totalBasketAmountHT += parseInt($(k).find('.priceTotalHT span[data-pricetoformat]').attr('data-pricetoformat'))
        totalBasketAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        subTotalFeesAmountTTC += parseInt($(k).find('.priceTotal').attr('data-pricetoformat'))
        LstTVA.push({ "taux": parseFloat($(k).find('.priceTotal').attr('data-tvataux').replace(',', '.')), "montant": parseInt($(k).find('.priceTotal').attr('data-tvaamount')) })
    })
    //sous-total events + produits
    $('#subTotalEventsProductsWrapper .priceTotal').attr('data-pricetoformat', subTotalEventsProduct)
    $('#subTotalEventsProductsWrapper .priceTotal').html(SetDeviseCode(subTotalEventsProduct))
    //sous-total produits suupplémentaire
    $('#subTotalMoreProductsWrapper .priceTotal').attr('data-pricetoformat', subTotalMoreProduct)
    $('#subTotalMoreProductsWrapper .priceTotal').html(SetDeviseCode(subTotalMoreProduct))
    //sous-total frais
    $('#subTotalFeesWrapper .priceTotal').attr('data-pricetoformat', subTotalFeesAmountTTC)
    $('#subTotalFeesWrapper .priceTotal').html(SetDeviseCode(subTotalFeesAmountTTC))
    //regroupe et additionne les tva à taux similaires
    var LstTVAReduced = [];
    LstTVA.reduce(function (res, value) {
        if (!res[value.taux]) {
            res[value.taux] = { taux: value.taux, montant: 0 };
            LstTVAReduced.push(res[value.taux])
        }
        res[value.taux].montant += value.montant;
        return res;
    }, {});
    //tri les tva par taux (du plus petit au plus grand)
    LstTVAReduced.sort(predicate({
        name: 'taux',
        reverse: false
    }));
    //écrit les tva
    $('#basketTotalCmdWrapper .collapseDetailsWrapper').html('')
    var templateTva = $('#hidden-template-tva-oneline').html();
    var totalTVA = 0
    $.each(LstTVAReduced, function (i, k) {
        if (k.taux > 0) {
            $('#basketTotalCmdWrapper .collapseDetailsWrapper').append(templateTva
                .replaceAll("[tvaTaux]", k.taux)
                .replaceAll("[tvaAmountCents]", k.montant)
                .replaceAll("[tvaAmount]", SetDeviseCode(k.montant))
            )
        }
        totalTVA += k.montant
    })
    $('#totalTVABigWrapper .totalTVAWrapper .priceTotal').attr('data-pricetoformat', totalTVA)
    $('#totalTVABigWrapper .totalTVAWrapper .priceTotal').html(SetDeviseCode(totalTVA))
    //ecrit le montant total du basket
    $('.bigTotalWrapper .priceTotal').attr('data-pricetoformat', totalBasketAmountTTC)
    $('.bigTotalWrapper .priceTotal').html(SetDeviseCode(totalBasketAmountTTC))
    //console.log('totalBasketAmountHT : ' + totalBasketAmountHT)
    //console.log('totalBasketAmountTTC : ' + totalBasketAmountTTC)
}

function LoginTunnel() {

}

function DrawPlanSeeMyPlacement(data) {
    if (data != '' && data != undefined) {
        $("#modalSeeMyPlacement .modal-body").html(data)

        $('#modalSeeMyPlacement #mapcontrols').hide()
        $('#modalSeeMyPlacement #resumePreShoppingCartPLAN').hide()
        $("#modalSeeMyPlacement").addClass('d-block')
        $("#modalSeeMyPlacement").css("cssText", "display: block !important; opacity: 1 !important;");
        if ($('#modalSeeMyPlacement #svgPlanSalle').children().length > 0) {
            var PlanMaxTop, PlanMaxLeft;
            $.each($('#modalSeeMyPlacement #svgPlanSalle').children(), function (i, k) {
                if (PlanMaxTop == undefined || $(k).attr('data-posy') < PlanMaxTop.attr('data-posy')) {
                    PlanMaxTop = $(k);
                }

                if (PlanMaxLeft == undefined || $(k).attr('data-posx') < PlanMaxLeft.attr('data-posx')) {
                    PlanMaxLeft = $(k);
                }
            })

            $.each($('#modalSeeMyPlacement #svgPlanSalle .textlong'), function (i, k) {
                var color = "#" + $(k).attr('data-fontcolor');
                var fontsize = $(k).data('fontsize') * 1.35 + "px";
                $(k).css('fill', color);
                $(k).css('font-size', fontsize);
                if ($(k).data('fontbold') == 1) {
                    $(k).css('font-weight', 'bold');
                }
            })
            var svgSize = $('#modalSeeMyPlacement #svgPlanSalle')[0].getBBox();
            $('#modalSeeMyPlacement #svgPlanSalle').css('height', svgSize.height).css('width', svgSize.width);
            if ($("#modalSeeMyPlacement #imgBackgroundSalle").length > 0 && $("#modalSeeMyPlacement #imgBackgroundSalle").attr('src') != "") {
                //plan avec image
                $('#modalSeeMyPlacement #divPlanSalleWrapperAll').css('width', ($("#modalSeeMyPlacement #imgBackgroundSalle").outerWidth()) + 'px');
                $('#modalSeeMyPlacement #eventMap, #modalSeeMyPlacement #eventMapInner, #modalSeeMyPlacement #divPlanSalleWrapperAll').css('height', ($("#modalSeeMyPlacement #imgBackgroundSalle").outerHeight()) + 'px');
                $('#modalSeeMyPlacement #divPlanSalleWrapperAll').attr('data-width', $('#modalSeeMyPlacement #divPlanSalleWrapperAll').width());
                $('#modalSeeMyPlacement #divPlanSalleWrapperAll').attr('data-height', $('#modalSeeMyPlacement #divPlanSalleWrapperAll').height());
                initScaleAndReplaceBigMap("#modalSeeMyPlacement");
                sendIframeSize()
                //img loaded
                $('#modalSeeMyPlacement #imgBackgroundSalle').on('load', function () {
                    $('#modalSeeMyPlacement #svgPlanSalle').css('height', $("#modalSeeMyPlacement #imgBackgroundSalle").outerHeight() + 'px').css('width', $("#modalSeeMyPlacement #imgBackgroundSalle").outerWidth() + 'px');
                    $('#modalSeeMyPlacement #divPlanSalleWrapperAll').css('width', ($("#modalSeeMyPlacement #imgBackgroundSalle").outerWidth()) + 'px');
                    $('#modalSeeMyPlacement #eventMap,#modalSeeMyPlacement #eventMapInner, #modalSeeMyPlacement #divPlanSalleWrapperAll').css('height', ($("#modalSeeMyPlacement #imgBackgroundSalle").outerHeight()) + 'px');
                    $('#modalSeeMyPlacement #divPlanSalleWrapperAll').attr('data-width', $('#modalSeeMyPlacement #divPlanSalleWrapperAll').width());
                    $('#modalSeeMyPlacement #divPlanSalleWrapperAll').attr('data-height', $('#modalSeeMyPlacement #divPlanSalleWrapperAll').height());
                    initScaleAndReplaceBigMap("#modalSeeMyPlacement");

                    $("#modalSeeMyPlacement").removeClass('d-block')
                    $("#modalSeeMyPlacement").removeAttr('style')

                    $("#modalSeeMyPlacement").modal('show')
                    sendIframeSize()
                });
            } else {
                //plan sans image
                $('#modalSeeMyPlacement #divPlanSalleWrapperAll').css('width', (svgSize.width) + 'px');
                $('#modalSeeMyPlacement #eventMap, #modalSeeMyPlacement #eventMapInner, #modalSeeMyPlacement #divPlanSalleWrapperAll').css('height', (svgSize.height) + 'px');
                $('#modalSeeMyPlacement #divPlanSalleWrapperAll').attr('data-width', $('#modalSeeMyPlacement #divPlanSalleWrapperAll').width());
                $('#modalSeeMyPlacement #divPlanSalleWrapperAll').attr('data-height', $('#modalSeeMyPlacement #divPlanSalleWrapperAll').height());
                initScaleAndReplaceBigMap("#modalSeeMyPlacement");

                $("#modalSeeMyPlacement").removeClass('d-block')
                $("#modalSeeMyPlacement").removeAttr('style')
                $("#modalSeeMyPlacement").modal('show')
            }
            $('#modalSeeMyPlacement #divPlanSalleWrapperAll').css('opacity', 1);
        }

        //DragBigMap()
        //lorsque l'utilisateur survol un siège sur le plan
        $('#svgPlanSalle circle.mine').off('show.bs.tooltip')
        $('#svgPlanSalle circle.mine').tooltip({
            template: '<div class="tooltip tooltip-lg tooltip-light tooltip-nopadding" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',
            container: 'body'
        })
        $('#svgPlanSalle circle.mine').on('show.bs.tooltip', function (e) {
            var template = $("#hidden-template-tooltip-viewfromseat").html()
            var iindex = $(e.target).attr('data-iindex')
            if (xmlVueFromSeat != undefined && $(xmlVueFromSeat).find('row[iindex="' + iindex + '"]') != undefined) {
                template = template.replaceAll('[img]', '<div class="imgViewFromSeat"><img id="tooltip_img_' + iindex + '" style="display: none;" src="' + $(xmlVueFromSeat).find('row[iindex="' + iindex + '"]').attr("web_picture") + '" /></div>')
            } else {
                template = template.replaceAll('[img]', "")
            }
            template = template.replaceAll('[ranktxt]', ((GetTranslationTerm(TranslationsList, "Widget_Session_LblRank") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblRank") : 'Rang'))
            template = template.replaceAll('[rank]', $(e.target).attr('data-rank'))
            template = template.replaceAll('[seattxt]', ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSeat") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSeat") : 'Siège'))
            template = template.replaceAll('[seat]', $(e.target).attr('data-seat'))
            template = template.replaceAll('[denominationtxt]', ((GetTranslationTerm(TranslationsList, "Widget_Basket_LblDenomination") != '') ? GetTranslationTerm(TranslationsList, "Widget_Basket_LblDenomination") : ''))            
            template = template.replaceAll('[denomination]', $(e.target).attr('data-denomination'))


            $(e.target).attr("data-original-title", template);
            //$('.tooltip_img_' + iindex).hide()

        })
        $('#svgPlanSalle circle.mine').on('inserted.bs.tooltip', function (e) {
            var iindex = $(e.target).attr('data-iindex')
            $('#tooltip_img_' + iindex).hide()
            $('#tooltip_img_' + iindex).on('load', function () {
                $('#tooltip_img_' + iindex).show()
                console.log('img loaded')
                $(e.target).tooltip('update')
            });
        })

    }
}