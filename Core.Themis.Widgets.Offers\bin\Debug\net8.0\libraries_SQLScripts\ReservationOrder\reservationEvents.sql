﻿/*
Declare @@OrderId int
declare @@identityId int

set @@identityId = 18
set @@OrderId = 126

--C'est fait cmde 1867 à 1870

*/

Declare @Manif_id int
Declare @SQL varchar(max)


DECLARE cursor_reservations CURSOR
FOR

/*
--select distinct  manifestation_id, commande_id  from Commande_Ligne where identite_id =@identityId and manifestation_id >0 and commande_id = @OrderId
select distinct  cl.manifestation_id, commande_id  
from Commande_Ligne cl
inner join seance s on cl.seance_id = s.seance_Id 
where identite_id = @identityId and cl.manifestation_id >0 and commande_id = @OrderId
and s.seance_date_deb > GETDATE()
*/

select distinct  cl.manifestation_id,cl. commande_id  
from Commande_Ligne cl
inner join seance s on cl.seance_id = s.seance_Id 
inner join Commande_Ligne_comp clc on cl.commande_id = clc.Commande_id and cl.manifestation_id = clc.Manifestation_ID and cl.dossier_id = clc.Dossier_ID
where identite_id = @identityId and cl.manifestation_id >0 and cl.commande_id = @OrderId
and s.seance_date_deb > GETDATE()
and clc.Etat <> 'A'


OPEN cursor_reservations

FETCH NEXT FROM cursor_reservations INTO @Manif_id,@OrderId

WHILE @@FETCH_STATUS = 0
BEGIN


set @SQL ='SELECT s.manifestation_id, m.manifestation_nom, s.seance_date_deb,		
		cmd.commande_id, dsvg.dossier_id,tt.type_tarif_id, tt.type_tarif_nom, c.categ_nom,c.categ_id,
		r.zone_id,
		zone.zone_nom,
		r.etage_id,
		etage.etage_nom,	
		r.section_id,
		section.section_nom
		,d.denom_nom, r.rang, r.siege,
		esvg.entree_etat, e.entree_id, e.seance_id, cmdl.abo_id, 
		abo.formule_id
		,form_abon_nom
		,tva.tva_libelle, tva.tva_taux
		,l.lieu_id, l.lieu_nom
		, case when consommateur_id IS NULL  then ''0'' else consommateur_id end
		,dsvg.identite_id
		,identDoss.identite_nom
		,identDoss.identite_prenom
		,cmdl.identite_id
		, CASE WHEN NEPASAFFICHERDATE like ''O'' THEN 0 ELSE 1 END as isShowSessionDate, 
           CASE WHEN NEPASAFFICHERDATE like ''H'' THEN 0 WHEN NEPASAFFICHERDATE like ''O'' THEN 0 ELSE 1 END as isShowSessionHour,
		     ISNULL((
			 select count(*)   from proprietes_of_manifs pm, proprietes_references_of_manifs pref
                         where pref.propriete_ref_id = pm.propriete_ref_id and valeur = 1
                          and pm.manifestation_id in (' + LTRIM(STR(@Manif_id)) + ')
                          and pref.code = ''ZapperEtag''
                          group by manifestation_id
			),0) as isZapperZE
			,
			ISNULL(( select count(*)  
 from proprietes_of_manifs pm, proprietes_references_of_manifs pref 
                      where pref.propriete_ref_id = pm.propriete_ref_id and valeur = 1
                      and pm.manifestation_id in (' + LTRIM(STR(@Manif_id)) + ') 
                      and pref.code = ''ZapperZES''),0) as isZapperZES,

		esvg.montant1 * 100 + esvg.montant2 * 100 +
		
                            case  when modecol4=''REMISE''  then - esvg.montant4* 100 
                        				when modecol4=''TAXE'' or modecol4=''COMMISSION'' then + esvg.montant4* 100 
                        				else 0 END 
                        				+
                         case  when modecol5=''REMISE''  then - esvg.montant5 * 100 
                        				when modecol5=''TAXE'' or modecol5=''COMMISSION'' then + esvg.montant5* 100 
                        				else 0 END +
                         case  when modecol6=''REMISE''   then - esvg.montant6 * 100 
                        				when modecol6=''TAXE'' or modecol6=''COMMISSION'' then + esvg.montant6* 100 
                        				else 0 END +
                        case  when modecol7=''REMISE''  then - esvg.montant7 * 100 
                        				when modecol7=''TAXE'' or modecol7=''COMMISSION'' then + esvg.montant7* 100 
                        				else 0 END +
                         case  when modecol8=''REMISE'' then - esvg.montant8 * 100 
                        				when modecol8=''TAXE'' or modecol8=''COMMISSION''  then + esvg.montant8* 100 
                        				else 0 END +
                         case  when modecol9=''REMISE''  then - esvg.montant9 * 100 
                        				when modecol9=''TAXE''  or modecol9=''COMMISSION'' then + esvg.montant9* 100 
                        				else 0 END +
                         case  when modecol10=''REMISE''  then - esvg.montant10 * 100 
                        				when modecol10=''TAXE'' or modecol10=''COMMISSION'' then + esvg.montant10* 100 
                        				else 0 END  as amount
		,isnull((select esvg1.montant1 * 100 + esvg1.montant2 * 100 +
		
                            case  when modecol4=''REMISE''  then - esvg1.montant4* 100 
                        				when modecol4=''TAXE'' or modecol4=''COMMISSION'' then + esvg1.montant4* 100 
                        				else 0 END 
                        				+
                         case  when modecol5=''REMISE''  then - esvg1.montant5 * 100 
                        				when modecol5=''TAXE'' or modecol5=''COMMISSION'' then + esvg1.montant5* 100 
                        				else 0 END +
                         case  when modecol6=''REMISE''   then - esvg1.montant6 * 100 
                        				when modecol6=''TAXE'' or modecol6=''COMMISSION'' then + esvg1.montant6* 100 
                        				else 0 END +
                        case  when modecol7=''REMISE''  then - esvg1.montant7 * 100 
                        				when modecol7=''TAXE'' or modecol7=''COMMISSION'' then + esvg1.montant7* 100 
                        				else 0 END +
                         case  when modecol8=''REMISE'' then - esvg1.montant8 * 100 
                        				when modecol8=''TAXE'' or modecol8=''COMMISSION''  then + esvg1.montant8* 100 
                        				else 0 END +
                         case  when modecol9=''REMISE''  then - esvg1.montant9 * 100 
                        				when modecol9=''TAXE''  or modecol9=''COMMISSION'' then + esvg1.montant9* 100 
                        				else 0 END +
                         case  when modecol10=''REMISE''  then - esvg1.montant10 * 100 
                        				when modecol10=''TAXE'' or modecol10=''COMMISSION'' then + esvg1.montant10* 100 
                        				else 0 END 
			from entreesvg_' + LTRIM(STR(@Manif_id)) + ' esvg1 inner join structure struc on 1=1 
			where esvg1.entree_id = esvg.entree_id and esvg1.dossier_v = (select max(esvg2.dossier_v) from entreesvg_' + LTRIM(STR(@Manif_id)) + ' esvg2
																		where esvg2.entree_id = esvg.entree_id) 
			
			and esvg1.entree_etat in(''P'')),0)  as amount_paid
		

		 FROM dossiersvg_' + LTRIM(STR(@Manif_id)) + ' dsvg
			inner join entreesvg_' + LTRIM(STR(@Manif_id)) + ' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v
			inner join structure struc on 1=1
			inner join seance s on esvg.seance_id=s.seance_Id
			INNER JOIN tva ON  tva.tva_id = s.taux_tva1_id
			inner join manifestation m on m.manifestation_id=s.manifestation_id and m.manifestation_id=' + LTRIM(STR(@Manif_id)) + '
			inner join type_tarif tt on esvg.type_tarif_id=tt.type_tarif_id
			inner join categorie c on esvg.categorie_id =c.categ_id
			inner join entree_' + LTRIM(STR(@Manif_id)) + ' e ON esvg.entree_id=e.entree_id and e.entree_etat <> ''L'' and esvg.dossier_id = e.dossier_id
			inner join commande cmd ON cmd.commande_id=dsvg.commande_id		
			inner join commande_ligne cmdl ON cmdl.commande_id=cmd.commande_id AND cmdl.dossier_id=dsvg.dossier_id  and cmdl.seance_id=s.seance_Id	
			left outer join abonnement abo on abo.abo_id =cmdl.abo_id
			left outer join  formule_abonnement fa on fa.form_abon_id = abo.formule_id
			inner join lieu l on l.lieu_id = s.lieu_id
			inner join REFERENCE_LIEU_PHYSIQUE R on REFERENCE_UNIQUE_PHYSIQUE_ID = REF_UNIQ_PHY_ID
			inner join zone on zone.zone_id = r.zone_id
			inner join etage on etage.etage_id = r.etage_id
			inner join section on section.section_id = r.section_id
			inner join DENOMINATION D on R.DENOMINATION_ID = D.DENOM_ID
			--LEFT OUTER JOIN gestion_place gp on gp.manif_id = m.manifestation_id  and gp.seance_id = s.seance_id and gp.categ_id = c.categ_id and gp.type_tarif_id = tt.type_tarif_id
			--inner join proprietes_of_manifs pm on pm.manifestation_id = m.manifestation_id
			--inner join proprietes_references_of_manifs pref on pm.propriete_ref_id =pref.propriete_ref_id

			LEFT OUTER JOIN Entree_Complement EC on EC.seance_id = s.seance_Id and EC.dossier_id = dsvg.dossier_id and EC.entree_id = e.entree_id
			
			LEFT OUTER join identite identDoss ON identDoss.identite_id = dsvg.identite_id
			LEFT OUTER JOIN  Produit_PDF_LOG_ENVOI pple on pple.Dossier_id = dsvg.dossier_id  
			AND pple.Identite_id = dsvg.identite_id AND pple.Entree_id = esvg.entree_id
			WHERE  cmd.commande_id=' + LTRIM(STR(@OrderId))
			+ ' AND type_ligne=''DOS'' 
			
					AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@Manif_id)) + ' esvg2 
		
			WHERE 
				 esvg.entree_id=esvg2.entree_id	and esvg2.dossier_id=esvg.dossier_id
				  and esvg.seance_id=s.seance_Id
				 )		 
			 AND (dsvg.dossier_etat = ''R'' or dsvg.type_operation like ''RESA%'') '
			
		PRINT @SQL
		EXEC (@SQL)


FETCH NEXT FROM cursor_reservations INTO @Manif_id,@OrderId

END

CLOSE cursor_reservations

DEALLOCATE cursor_reservations

