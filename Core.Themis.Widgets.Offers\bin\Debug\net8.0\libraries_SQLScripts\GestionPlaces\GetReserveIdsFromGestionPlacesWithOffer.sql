--DECLARE @pOfferId INT = 95
--DECLARE @pCategoryId INT = 150002
--DECLARE @pSessionId INT = 10175

CREATE TABLE #reserves
(
	reserve_id INT,
	aucune_reserve BIT NULL
)

INSERT INTO #reserves
	SELECT ISNULL(gpr.reserve_id, 0), gp.aucune_reserve
	FROM gestion_place gp
	INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
	LEFT JOIN gestion_place_reserve gpr ON gp.gestion_place_id = gpr.gestion_place_id
	WHERE gp.categ_id = @pCategoryId
	AND gp.seance_id = @pSessionId
	AND offre_id = @pOfferId

IF (1 = ANY (SELECT aucune_reserve FROM #reserves))
BEGIN
	INSERT INTO #reserves VALUES (0, NULL)
END

SELECT reserve_id FROM #reserves

DROP TABLE #reserves