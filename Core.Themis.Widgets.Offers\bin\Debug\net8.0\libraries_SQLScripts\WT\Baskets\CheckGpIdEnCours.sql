       --cmdWT.Parameters.AddWithValue("pIdentiteId", identityId);
		--cmdWT.Parameters.AddWithValue("pPriceId", ThisGestionPlaceEntity.PriceId);
		--cmdWT.Parameters.AddWithValue("pSessionId", ThisGestionPlaceEntity.SessionId);
          --                              cmdWT.Parameters.AddWithValue("pEventId", ThisGestionPlaceEntity.EventId);


/* CheckGpIdEnCours */
/* ******* verif que le consumer n'a pas déjà ce tarif, cette manif dans son panier */

declare @n int =0
select @n = count(*) from panier_entree pe
inner join panier p on p.panier_id = pe.panier_id 
where p.etat ='C'
and pe.consumer_id =@pidentiteid
and pe.type_tarif_id = @ppriceid 
and pe.manif_id = @peventid

if @n = 0
begin
	select 'ok' as ret
end
else
begin
	select 'ko' as ret
end




