﻿/* D:\DEV\EventsCatalog_jv\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers */
/* ..\..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\LIBRARIES_SQLSCRIPTS\EventsSessions\GetEventsGeneralsAndAdhesionsOfferByFilter.sql */ 
/*
//TODO filtrer requete qui insert table temporaire par le eventTypeId

DECLARE @pEventsTypesId VARCHAR(MAX) = '0,3'
DECLARE @pEventsGroupesId VARCHAR(MAX) = ''
DECLARE @pGenresId VARCHAR(MAX) ='1,2,3,5,0'
DECLARE @pSousGenresId VARCHAR(MAX) =''
DECLARE @pCiblesId VARCHAR(MAX) = ''
DECLARE @pSortBy VARCHAR(MAX) = 'priceasc' --sessionfirstdate, sessionlastdate, nameasc, namedesc, pricefrom
DECLARE @pSearchTerm VARCHAR(MAX) = ''
DECLARE @pBuyerProfilId INT = 0
DECLARE @pStartDate DATETIME = '' -- '01-09-2024'
DECLARE @pEndDate DATETIME = '' --'01-01-2025'
DECLARE @pIdentiteId INT = 0

DECLARE @pLangCode VARCHAR(MAX) = 'fr'
DECLARE @pFormulasId VARCHAR(MAX) = '3, 4, 5'
*/

DECLARE @language_id INT = ISNULL((SELECT langue_id FROM langue WHERE langue_code = @pLangCode), 1)


CREATE TABLE #myoffres
(
offre_id INT,
offre_nom VARCHAR(200)
)
EXEC [Sp_ws_getoffres_totable]
@pIdentiteId,
@pBuyerProfilId

DECLARE @sOffresId NVARCHAR(MAX)

SELECT @sOffresId = ISNULL(
STUFF(
( SELECT ',' + CONVERT(NVARCHAR(20), offre_id)
FROM #myoffres
FOR xml path('')
) , 1 , 1 , '')
,'')


--DECLARE @isrevendeur INT = 0
--SELECT @isrevendeur = COUNT(*) FROM profil_acheteur pa WHERE pa.id = @pBuyerProfilId AND pa.is_revendeur = 1

--DECLARE @addgeneralsrules INT = 0 /* 0 = on n'ajoute pas l'offre generale, 1 on ajoute */


--DECLARE @sqlColonne NVARCHAR(500)
--SET @sqlColonne = 'SELECT @n = COUNT(*) FROM profil_acheteur pa WHERE pa.id = ' + CONVERT(VARCHAR(10), @pBuyerProfilId) + N' AND pa.add_generals_rules = 1'

--BEGIN TRY
--EXEC sp_executesql @sqlColonne, N'@n int out', @addgeneralsrules OUT
--END TRY
--BEGIN CATCH
--END CATCH


DECLARE @addgeneralsrulesForBuyerProfil BIt = ISNULL( (SELECT add_generals_rules FROM profil_acheteur pa WHERE pa.id = CONVERT(VARCHAR(10), @pBuyerProfilId) ), 0)
DECLARE @addgeneralsrulesForOffer BIT =  ISNULL( (SELECT add_generals_rules FROM offre o WHERE o.offre_id IN ( SELECT offre_id FROM #myoffres) ), 0)



DECLARE @TempTableManifs TABLE (
	nom VARCHAR(MAX) NULL,
	manifestation_id int NULL,
	manifestation_descrip VARCHAR(MAX) NULL,
	manifestation_descrip2 VARCHAR(MAX) NULL, 
	manifestation_descrip3 VARCHAR(MAX) NULL,
	manifestation_descrip4 VARCHAR(MAX) NULL,
	manifestation_descrip5 VARCHAR(MAX) NULL,
	ID_genre INT NULL, 
	manifestation_groupe_id INT NULL,
	formula_id INT NULL,
	nb_manif_min INT NULL,
	nb_manif_max INT NULL)

IF (@pFormulasId <> '') 
BEGIN
	INSERT INTO @TempTableManifs (formula_id, nom, nb_manif_min, nb_manif_max)
		SELECT DISTINCT fa.form_abon_id, ISNULL(tfa.form_abon_nom, fa.form_abon_nom), fa.form_abon_manifmin AS nb_manif_min, fa.form_abon_manifmax AS nb_manif_max
		FROM formule_abonnement fa
		INNER JOIN gestion_place_DispoAbos gpda ON gpda.formule_id = fa.form_abon_id
		LEFT OUTER JOIN traduction_formule_abonnement tfa ON tfa.form_abon_id = fa.form_abon_id  AND tfa.langue_id = @language_id
		WHERE fa.form_abon_id in(SELECT name FROM splitstring(@pFormulasId, ','))
		AND forcer = 1  
		AND 1 = ALL (SELECT gpAll.isvalide 
					 FROM gestion_place gpAll 
					 WHERE gpAll.formule_id = fa.form_abon_id) 
END

IF(@pEventsTypesId <> '')
BEGIN
	IF (@sOffresId ='') --Sans offre
	BEGIN
		INSERT INTO @TempTableManifs
		SELECT ISNULL(tm.manifestation_nom, manifs.manifestation_nom) AS nom, 
		manifs.manifestation_id, 
		ISNULL(tm.manifestation_descrip, manifs.manifestation_descrip),
		ISNULL(tm.manifestation_descrip2, manifs.manifestation_descrip2), 
		ISNULL(tm.manifestation_descrip3, manifs.manifestation_descrip3), 
		ISNULL(tm.manifestation_descrip4, manifs.manifestation_descrip4),
		ISNULL(tm.manifestation_descrip5, manifs.manifestation_descrip5),  
		manifs.ID_genre, 
		manifs.manifestation_groupe_id, 
		NULL, 
		NULL, 
		NULL
		FROM (
			
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 0
			AND formule_id IS NULL
			UNION
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN adhesion_catalog_offresliees o ON ogp.offre_id = o.offre_id
			left  JOIN offre_contrainte oc ON oc.offre_id = o.offre_id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			AND oc.contrainte_id IS NULL /* offres adh sans contraintes */
			AND formule_id IS NULL
		) manifs
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
		LEFT OUTER JOIN traduction_manifestation tm ON tm.manifestation_id = manifs.manifestation_id AND langue_id = @language_id
		WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
	END

	IF (@sOffresId <> '') --Avec offre
	BEGIN
		INSERT INTO @TempTableManifs
		SELECT ISNULL(tm.manifestation_nom, manifs.manifestation_nom) AS nom, 
		manifs.manifestation_id, 
		ISNULL(tm.manifestation_descrip, manifs.manifestation_descrip),
		ISNULL(tm.manifestation_descrip2, manifs.manifestation_descrip2), 
		ISNULL(tm.manifestation_descrip3, manifs.manifestation_descrip3), 
		ISNULL(tm.manifestation_descrip4, manifs.manifestation_descrip4),
		ISNULL(tm.manifestation_descrip5, manifs.manifestation_descrip5), 
		manifs.ID_genre, 
		manifs.manifestation_groupe_id, 
		NULL, 
		NULL, 
		NULL
		FROM (
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id 
			INNER JOIN #myoffres myo ON myo.offre_id =ogp.offre_Id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			AND formule_id IS NULL
			
		) manifs
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
		LEFT OUTER JOIN traduction_manifestation tm ON tm.manifestation_id = manifs.manifestation_id AND langue_id = @language_id
		WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))


		IF(@addgeneralsrulesForOffer =1)
		BEGIN
		INSERT INTO @TempTableManifs
			SELECT ISNULL(tm.manifestation_nom, manifs.manifestation_nom) AS nom, 
			manifs.manifestation_id, 
			ISNULL(tm.manifestation_descrip, manifs.manifestation_descrip),
			ISNULL(tm.manifestation_descrip2, manifs.manifestation_descrip2), 
			ISNULL(tm.manifestation_descrip3, manifs.manifestation_descrip3), 
			ISNULL(tm.manifestation_descrip4, manifs.manifestation_descrip4),
			ISNULL(tm.manifestation_descrip5, manifs.manifestation_descrip5), 
			manifs.ID_genre, manifs.manifestation_groupe_id, 
			NULL, 
			NULL, 
			NULL
			FROM (
				SELECT DISTINCT m.* FROM manifestation m
				INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
				INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
				WHERE gp.isvalide = 1 
				AND s.seance_cloturer = 'N' 
				AND s.seance_verrouiller = 'N' 
				AND s.supprimer = 'N' 
				AND m.supprimer = 'N'
				AND isContrainteIdentite = 0
				AND formule_id IS NULL
			) manifs
			INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
			LEFT OUTER JOIN traduction_manifestation tm ON tm.manifestation_id = manifs.manifestation_id AND langue_id = @language_id
			WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))

		END


		IF (@addgeneralsrulesForBuyerProfil =1) -- on ajoute les manifs tout publics
		BEGIN
			INSERT INTO @TempTableManifs
			SELECT ISNULL(tm.manifestation_nom, manifs.manifestation_nom) AS nom, 
			manifs.manifestation_id, 
			ISNULL(tm.manifestation_descrip, manifs.manifestation_descrip),
			ISNULL(tm.manifestation_descrip2, manifs.manifestation_descrip2), 
			ISNULL(tm.manifestation_descrip3, manifs.manifestation_descrip3), 
			ISNULL(tm.manifestation_descrip4, manifs.manifestation_descrip4),
			ISNULL(tm.manifestation_descrip5, manifs.manifestation_descrip5), 
			manifs.ID_genre, manifs.manifestation_groupe_id, 
			NULL, 
			NULL, 
			NULL
			FROM (
				SELECT DISTINCT m.* FROM manifestation m
				INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
				INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
				WHERE gp.isvalide = 1 
				AND s.seance_cloturer = 'N' 
				AND s.seance_verrouiller = 'N' 
				AND s.supprimer = 'N' 
				AND m.supprimer = 'N'
				AND isContrainteIdentite = 0
				AND formule_id IS NULL
			) manifs
			INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
			LEFT OUTER JOIN traduction_manifestation tm ON tm.manifestation_id = manifs.manifestation_id AND langue_id = @language_id
			WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
		END
	END
END

IF @pCiblesId <> ''
BEGIN
	DECLARE @ManifCible TABLE (manif_id INT)
	SELECT name INTO #tmpCibleToSearch FROM splitstring (@pCiblesId,',') c

	IF (SELECT COUNT(*) FROM #tmpCibleToSearch WHERE name = 0) > 0
	BEGIN
		INSERT INTO @ManifCible
		SELECT DISTINCT m.manifestation_id FROM @TempTableManifs m
		INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
		LEFT OUTER JOIN Seance_Cible sc ON sc.Seance_id = s.seance_Id
		WHERE sc.Cible_id  IN (SELECT * FROM #tmpCibleToSearch)
		OR sc.Cible_id IS NULL

		DELETE @TempTableManifs FROM @TempTableManifs m WHERE m.manifestation_id NOT IN (SELECT Manif_id FROM @ManifCible)
	END
	ELSE 
	BEGIN
		INSERT INTO @ManifCible
		SELECT DISTINCT m.manifestation_id FROM @TempTableManifs m
		INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
		LEFT OUTER JOIN Seance_Cible sc ON sc.Seance_id = s.seance_Id
		WHERE sc.Cible_id  IN (SELECT * FROM #tmpCibleToSearch)

		DELETE @TempTableManifs FROM @TempTableManifs m WHERE m.manifestation_id NOT IN (SELECT Manif_id FROM @ManifCible)
	END

	-- si il y a un id -1 alors on remonte tous même les abonnements
	IF(SELECT COUNT(*) FROM #tmpCibleToSearch WHERE Name = -1) = 0
	BEGIN
		DELETE @TempTableManifs FROM @TempTableManifs WHERE formula_id IS NOT NULL
	END

	DROP TABLE #tmpCibleToSearch
END

IF @pEventsTypesId  <> ''
BEGIN
	SELECT name INTO #tmpEventTypesToSearch FROM splitstring (@pEventsTypesId,',') c

	DELETE @TempTableManifs FROM @TempTableManifs m
	INNER JOIN manifestation_groupe mg ON mg.manif_groupe_id = m.manifestation_groupe_id
	WHERE mg.type_evenement NOT IN (SELECT * FROM #tmpEventTypesToSearch) 
	
	DROP TABLE #tmpEventTypesToSearch
END

IF @pEventsGroupesId <> ''
BEGIN
	SELECT name INTO #tmpEventGroupToSearch FROM splitstring (@pEventsGroupesId,',') c

	DELETE @TempTableManifs FROM @TempTableManifs m
	INNER JOIN manifestation_groupe mg ON mg.manif_groupe_id = m.manifestation_groupe_id
	WHERE mg.manif_groupe_id NOT IN (SELECT * FROM #tmpEventGroupToSearch)

	-- si il y a un id -1 alors on remonte tous même les abonnements
	IF(SELECT COUNT(*) FROM #tmpEventGroupToSearch WHERE Name = -1) = 0
	BEGIN
		DELETE @TempTableManifs FROM @TempTableManifs WHERE formula_id IS NOT NULL
	END

	DROP TABLE #tmpEventGroupToSearch
END

IF @pGenresId  <> ''
BEGIN
	SELECT name INTO #tmpGenresIds FROM splitstring (@pGenresId,',') c

	DECLARE @cntGenreAucunGroupe INT =  (SELECT COUNT(*) FROM #tmpGenresIds WHERE Name = 0)

	IF @cntGenreAucunGroupe > 0
	BEGIN
		DECLARE @ManifGenreWithAucunGroupe TABLE(manif_id INT)
		INSERT INTO @ManifGenreWithAucunGroupe
		SELECT m.manifestation_id FROM manifestation m
		INNER JOIN manifestation_genre mg ON mg.id = m.ID_genre
		INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
		WHERE mgg.id IN (SELECT * FROM #tmpGenresIds)

		UNION				

		SELECT m.manifestation_id FROM manifestation m
		WHERE m.ID_genre = 0

		DELETE @TempTableManifs FROM @TempTableManifs m  WHERE m.manifestation_id NOT IN (SELECT manif_id FROM @ManifGenreWithAucunGroupe)
	END
	ELSE
	BEGIN
		DECLARE @ManifGenre TABLE(manif_id INT)

		INSERT INTO @ManifGenre
		SELECT m.manifestation_id FROM manifestation m
		INNER JOIN manifestation_genre mg ON mg.id = m.ID_genre
		INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
		WHERE mgg.id IN (SELECT * FROM #tmpGenresIds)
	
		DELETE @TempTableManifs FROM @TempTableManifs m  WHERE m.manifestation_id NOT IN (SELECT manif_id FROM @ManifGenre)
	END
	
		-- si il y a un id -1 alors on remonte tous même les abonnements
	IF(SELECT COUNT(*) FROM #tmpGenresIds WHERE Name = -1) = 0
	BEGIN
		DELETE @TempTableManifs FROM @TempTableManifs WHERE formula_id IS NOT NULL
	END

	DROP TABLE #tmpGenresIds
END

IF @pSousGenresId  <> ''
BEGIN
	SELECT name INTO #tmpSousGenresIds FROM splitstring (@pSousGenresId,',') c

	SELECT name INTO #tmpGenresIdsInSubGenre FROM splitstring (@pGenresId,',') c

	DECLARE @cntGenreWithoutGroupe INT =  (SELECT COUNT(*) FROM #tmpGenresIdsInSubGenre WHERE Name = 0)

	DECLARE @ManifSousGenre TABLE(manif_id int)

	INSERT INTO @ManifSousGenre
	SELECT m.manifestation_id  FROM manifestation m
	INNER JOIN manifestation_genre mg ON mg.id = m.ID_genre
	WHERE mg.id IN (SELECT * FROM #tmpSousGenresIds)

	IF @cntGenreAucunGroupe > 0 AND (SELECT COUNT(*) FROM #tmpGenresIdsInSubGenre WHERE Name = -1) = 0
	BEGIN
		DELETE @TempTableManifs 
		FROM @TempTableManifs m  
		WHERE m.manifestation_id NOT IN (SELECT manif_id FROM @ManifSousGenre)
		AND m.ID_genre <> 0
	END
	ELSE
	BEGIN
		DELETE @TempTableManifs 
		FROM @TempTableManifs m  
		WHERE m.manifestation_id NOT IN (SELECT manif_id FROM @ManifSousGenre)
	END
	
	DELETE @TempTableManifs FROM @TempTableManifs WHERE formula_id IS NOT NULL

	DROP TABLE #tmpSousGenresIds
	DROP TABLE #tmpGenresIdsInSubGenre
END

IF @pSearchTerm <> ''
BEGIN
	DELETE @TempTableManifs FROM @TempTableManifs m
	WHERE m.nom NOT LIKE '%'+ @pSearchTerm + '%'
END

IF @pStartDate <> '' AND @pEndDate <> ''
BEGIN
	DECLARE @ManifDateRange TABLE(manif_id INT)

	INSERT INTO @ManifDateRange
	SELECT distinct m.manifestation_id  FROM @TempTableManifs m
	INNER JOIN seance s ON s.manifestation_id = m.manifestation_id 
	INNER JOIN gestion_place gp ON gp.seance_id = s.seance_id AND gp.manif_id = m.manifestation_id 
	WHERE gp.isvalide = 1  
	AND  s.seance_date_deb >= @pStartDate 
	AND s.seance_date_fin <= @pEndDate
	
	DELETE @TempTableManifs FROM @TempTableManifs m  WHERE m.manifestation_id NOT IN (SELECT manif_id FROM @ManifDateRange)

	DELETE @TempTableManifs FROM @TempTableManifs m WHERE formula_id IS NOT NULL
END

IF  @pSortBy = 'sessionfirstdate'
BEGIN
	SELECT * FROM (
		SELECT DISTINCT m.*, MIN(seance_date_deb)  AS sessionFirstDate
		FROM @TempTableManifs m
		INNER JOIN seance s ON s.manifestation_id = m.manifestation_id 
		INNER JOIN gestion_place gp ON gp.seance_id = s.seance_id
		WHERE formula_id IS NULL	
		AND s.seance_cloturer = 'N' 
		AND s.supprimer = 'N' 
		AND s.seance_verrouiller = 'N' 
		AND gp.isvalide = 1
		GROUP BY m.manifestation_id, m.nom, manifestation_descrip,
		manifestation_descrip2, manifestation_descrip3, manifestation_descrip4, manifestation_descrip5,  ID_genre, manifestation_groupe_id, formula_id, nb_manif_min, nb_manif_max
		UNION
		SELECT DISTINCT m.*, CAST('' AS DATETIME) AS sessionFirstDate
		FROM @TempTableManifs m
		WHERE manifestation_id IS NULL
	) AS t
	ORDER BY sessionFirstDate
END

IF  @pSortBy = 'sessionlastdate'
BEGIN
	SELECT * FROM (
		SELECT DISTINCT m.*, MAX(seance_date_fin) AS sessionLastDate
		FROM @TempTableManifs m
		INNER JOIN seance s ON s.manifestation_id = m.manifestation_id 
		INNER JOIN gestion_place gp ON gp.seance_id = s.seance_id
		WHERE formula_id IS NULL	
		AND s.seance_cloturer = 'N' 
		AND s.supprimer = 'N' 
		AND s.seance_verrouiller = 'N' 
		AND gp.isvalide = 1
		GROUP BY m.manifestation_id, m.nom, manifestation_descrip,
		manifestation_descrip2, manifestation_descrip3, manifestation_descrip4, manifestation_descrip5,  ID_genre, manifestation_groupe_id, formula_id, nb_manif_min, nb_manif_max
		UNION
		SELECT DISTINCT m.*, CAST(''  AS DATETIME) AS sessionLastDate
		FROM @TempTableManifs m
		WHERE manifestation_id IS  NULL
	) AS t
	ORDER BY sessionLastDate DESC
END

IF  @pSortBy = 'nameasc'
BEGIN
	SELECT * FROM @TempTableManifs m
	ORDER BY CONCAT('N' , nom) ASC 
END

IF  @pSortBy = 'namedesc'
BEGIN
	SELECT * FROM @TempTableManifs m
	ORDER BY CONCAT('N' , nom) DESC 
END

DECLARE @nbOffre INT 
SELECT @nbOffre = COUNT(*) FROM #myoffres
	
IF @nbOffre > 0
BEGIN
	IF  @pSortBy = 'priceasc'
	BEGIN
		SELECT * FROM (
			SELECT DISTINCT m.*, lpe.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceEvent lpe ON lpe.manif_id = m.manifestation_id
			INNER JOIN #myoffres o ON o.offre_id = lpe.offre_id
			WHERE m.manifestation_id IS NOT NULL
			UNION
			SELECT DISTINCT m.*, lpa.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceAbo lpa ON lpa.abo_id = formula_id
			WHERE m.formula_id IS NOT NULL
		) AS T
		ORDER BY  amount ASC
	END

	IF  @pSortBy = 'pricedesc'
	BEGIN
		-- y a t-il une offre dans les abo fermés ?????
		SELECT * FROM (
			SELECT DISTINCT m.*, lpe.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceEvent lpe ON lpe.manif_id = m.manifestation_id  
			INNER JOIN #myoffres o ON o.offre_id = lpe.offre_id
			WHERE m.manifestation_id IS NOT NULL
			UNION
			SELECT DISTINCT m.*, lpa.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceAbo lpa ON lpa.abo_id = formula_id
			INNER JOIN #myoffres o ON o.offre_id = lpa.offre_id
			WHERE m.formula_id IS NOT NULL
		) AS T
		ORDER BY  amount DESC
	END
END
ELSE
BEGIN
	if  @pSortBy = 'priceasc'
	BEGIN
		SELECT * FROM (
			SELECT DISTINCT m.*, lpe.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceEvent lpe ON lpe.manif_id = m.manifestation_id  AND offre_id IS NULL
			WHERE m.manifestation_id IS NOT NULL
			UNION
			SELECT DISTINCT m.*, lpa.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceAbo lpa ON lpa.abo_id = formula_id  AND offre_id IS NULL
			WHERE m.formula_id IS NOT NULL
		) AS T
		ORDER BY  amount ASC
	END

	IF  @pSortBy = 'pricedesc'
	BEGIN
		SELECT * FROM (
			SELECT DISTINCT m.*, lpe.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceEvent lpe ON lpe.manif_id = m.manifestation_id  AND offre_id IS NULL
			WHERE m.manifestation_id IS NOT NULL
			UNION
			SELECT DISTINCT m.*, lpa.amount
			FROM @TempTableManifs m
			LEFT OUTER JOIN LowerPriceAbo lpa ON lpa.abo_id = formula_id  AND offre_id IS NULL
			WHERE m.formula_id IS NOT NULL
		) AS T
		ORDER BY  amount DESC
	END
END

DROP TABLE #myoffres
 
