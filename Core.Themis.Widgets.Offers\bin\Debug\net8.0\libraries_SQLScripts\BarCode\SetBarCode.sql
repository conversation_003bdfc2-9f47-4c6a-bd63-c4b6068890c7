
-- DECLARE @pbarCode VARCHAR(100)
-- DECLARE @porigine VARCHAR(100)
-- DECLARE @pIdEvent INT = 283
-- DECLARE @pColEmail INT = 6

-- SET @pbarCode='4604300736'                                                                  
-- SET @porigine='2000030084'

DECLARE @numbillet INT

-- Set the billet number
SELECT TOP 1 @numbillet = numbillet 
FROM recette 
WHERE ((externe = @porigine AND motif='') OR (motif = @porigine AND externe=''))
AND manifestation_id = @pIdEvent
AND recette_id = (select MAX(recette_id) from recette rhisto where rhisto.entree_id = recette.entree_id and rhisto.seance_id = recette.seance_id) -- check que c'est bien la derniere recette

ORDER BY recette_id DESC

--SET @numbillet = NULL

IF @numbillet IS NOT NULL and @numbillet > 0
	BEGIN
	-- Insert new data in recette historic
	INSERT INTO recette_histo_updatesExternes (recette_id, numbillet, old_motif, old_externe, externe, origine, partner_name)
	SELECT recette_id, 
		   numbillet, 
		   motif, 
		   externe, 
		   @pbarCode, 
		   @porigine,
		   @partnerCode
	FROM recette 
	WHERE numbillet = @numbillet 
	AND ( motif = @porigine OR externe = @porigine )
	AND type_operation='E'

	-- Update recette with new barcode
	UPDATE recette SET externe = @pBarCode, motif = '' WHERE numbillet = @numbillet
    
	END