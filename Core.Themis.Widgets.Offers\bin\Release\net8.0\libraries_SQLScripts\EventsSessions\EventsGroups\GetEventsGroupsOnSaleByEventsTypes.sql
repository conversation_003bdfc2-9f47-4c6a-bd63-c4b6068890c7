﻿/*
declare @pEventsTypesId varchar(max) = '2,0'
*/

select distinct mg.*
from manifestation_groupe mg
INNER JOIN manifestation m ON m.manifestation_groupe_id = mg.manif_groupe_id
INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
where gp.isvalide = 1 AND  type_evenement in (select Name from splitstring(@pEventsTypesId, ','))


/*

select distinct mg.*
from manifestation_groupe mg
INNER JOIN manifestation m ON m.manifestation_groupe_id = mg.manif_groupe_id
INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
where gp.isvalide = 1 AND  type_evenement in (select Name from splitstring(pEventsTypesId, ','))



IF @pEventTypeId = -1
BEGIN


	select distinct mg.*
	from manifestation_groupe mg
	INNER JOIN manifestation m ON m.manifestation_groupe_id = mg.manif_groupe_id
	INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
	where gp.isvalide = 1

END
ELSE
BEGIN

	select distinct mg.*
	from manifestation_groupe mg
	INNER JOIN manifestation m ON m.manifestation_groupe_id = mg.manif_groupe_id
	INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
	where gp.isvalide = 1 AND  type_evenement = @pEventTypeId

END*/