/*
declare @pLangCode varchar(max)
set @pLangCode='fr'
*/

declare @langCode varchar(max)
set @langCode=@plangCode


DECLARE @langue_id int = (select langue_id from langue where langue_code = @langCode)


select mg.*, '' as split_manifestation,  m.*, '' as split_trad_manif_group, trad_manif_group.*, '' as split_trad_manif, trad_manif.*
from manifestation_groupe mg
left outer join manifestation m on m.manifestation_groupe_id = mg.manif_groupe_id
inner join seance s on s.manifestation_id = m.manifestation_id and s.seance_date_fin > getdate()
LEFT JOIN traduction_manifestation_groupe trad_manif_group on mg.manifestation_id = trad_manif_group.manifestation_id 
		and trad_manif_group.langue_id = @langue_id 
LEFT JOIN traduction_manifestation trad_manif on mg.manifestation_id = trad_manif.manifestation_id 
			and trad_manif.langue_id = @langue_id 
where mg.supprimer <> 'O'  and m.supprimer <> 'O' 
and m.supprimer <> 'O' and s.seance_masquer <>'O' and s.seance_cloturer <> 'O' and s.seance_verrouiller <> 'O'





