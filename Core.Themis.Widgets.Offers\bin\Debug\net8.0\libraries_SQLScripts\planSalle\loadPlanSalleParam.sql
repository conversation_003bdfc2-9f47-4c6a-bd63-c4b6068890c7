﻿/********* LOAD PLAN SALLE *********/





if (@pFirstzoneid >0)

	select zone_id into #myZones FROM zone WHERE zone_id in ({listzonesId})

if (@pFirstfoorId >0)
begin
	select etage_id into #myFloors FROM etage WHERE etage_id in ({listfloorsId})
end

if (@pFirstsectionId >0)
begin
	select section_id into #mySections FROM section WHERE section_id in ({listsectionsId})
end





declare @SQL varchar(max)
set @SQL = 'SELECT  seance_id, e.entree_id,
rlp.siege as seat, rlp.rang as row, rlp.orientation, rlp.type_siege, pos_x, pos_y 
, decal_x, decal_y, orientation, 
z.zone_id, et.etage_id,sect.section_id,
cat.categ_id,
CASE WHEN
entree_etat=''L'' AND (flag_selection is null or flag_selection='''')
AND e.contingent_id=0 AND e.alotissement_id=0
THEN ''O'' ELSE ''N'' END as IsFree, '
  
if @pWebUserId = 0
begin
	set @SQL += ' ''N''  as IsMine '
end

if @pWebUserId > 0
begin 
	set @SQL += 'case when flag_selection like ''%'+CONVERT(varchar(max), @pWebUserId)+'%'' then ''O'' else ''N'' end as IsMine '
end

set @SQL += ',rlp.iindex
,reserve_id
,rlp.denomination_id, den.denom_nom, rlp.bordure, e.entree_etat,e.alotissement_id,e.contingent_id
FROM entree_[eventID] e
INNER JOIN reference_lieu_physique rlp ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id
INNER JOIN zone z ON z.zone_id = rlp.zone_id 
INNER JOIN etage et ON et.etage_id = rlp.etage_id 
INNER JOIN section sect ON sect.section_id = rlp.section_id
INNER JOIN categorie cat ON cat.categ_id=e.categorie_id
INNER JOIN denomination den ON den.denom_id = rlp.denomination_id
WHERE entree_etat<>''X'' and entree_etat<>''I'' 
	 AND e.seance_id=' + CONVERT(varchar(50), @pSessionId) 

	  
if @pFirstzoneid > 0
begin
	set @SQL += ' AND z.zone_id in (select zone_id from #myZones)'
end

if @pFirstfoorId > 0
begin
	set @SQL += ' AND et.etage_id in (select etage_id from #myFloors)'
end

if @pFirstsectionId > 0
begin
	set @SQL += ' AND sect.section_id in (select section_id from #mySections)'
end

print @sql
exec(@SQL)

	  -- AND z.zone_id=0  AND et.etage_id=0
	   -- AND sect.section_id=0  


