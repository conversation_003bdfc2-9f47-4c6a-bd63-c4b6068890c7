﻿
SELECT m.manifestation_id as event_id, convert(int, seance_Id) as session_id,
	m.manifestation_nom as event_name, s.seance_date_deb as session_date,
	l.lieu_id as place_id, l.lieu_nom as place_name, l.lieu_rue1 as place_street1,
	l.lieu_rue2 as place_street2,
	l.lieu_rue3 as place_street3,
	l.lieu_rue4 as place_street4,
	l.lieu_cp as place_zip,
	l.lieu_ville as place_city
	
	--
/* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
  FROM seance s
  INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
  INNER JOIN lieu l on l.lieu_id = s.lieu_id
  WHERE seance_date_fin > getdate()
	AND (seance_date_deb<dateadd(year, 10, getdate())
	OR seance_date_deb >  Convert(datetime, '2099-01-01' )) -- date libre
    AND seance_verrouiller = 'N'
	  AND s.supprimer <> 'O'
ORDER BY m.manifestation_id, s.seance_date_deb


DECLARE cursorManifs CURSOR FOR
SELECT distinct[manifestation_id] /* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
  FROM [seance] 
  WHERE seance_date_fin > getdate()
 AND (seance_date_deb<dateadd(year, 10, getdate())
  OR seance_date_deb >  Convert(datetime, '2099-01-01' )) -- date libre
  AND seance_verrouiller = 'N'

DECLARE @MANIFID INT

DECLARE @tableSess TABLE (session_id int, seance_date datetime, price_id int, price_name varchar(50), category_id int, category_name varchar(50), 
	amount decimal, 
	amountHC decimal,
	charge decimal,
	tax decimal,
	discount decimal,
	commission decimal,
	ticketAmount decimal
)

OPEN cursorManifs;
FETCH  NEXT FROM cursorManifs INTO @MANIFID;
WHILE(@@FETCH_STATUS=0)
	BEGIN

		declare @sql varchar(max)
		set @sql = 'SELECT
			vts.seance_id ,
			s.seance_date_deb
			,tt.type_tarif_id, tt.type_tarif_nom
			,cat.categ_id, cat.categ_nom
			,TotalAmount= (vts.vts_grille1+vts.vts_grille2+case when modecol4=''REMISE'' then - vts.vts_grille4  when modecol4=''TAXE'' or modecol4=''COMMISSION'' then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then vts.vts_grille10 else 0 END	 )
			,vts.vts_grille1 as AmountExceptTax
			,vts.vts_grille2 as Charge 
			,Tax= case when modecol4=''TAXE'' then vts.vts_grille4 else 0 END + case  when modecol5=''TAXE'' then vts.vts_grille5 else 0 END + case  when modecol6=''TAXE'' then vts.vts_grille6 else 0 END + case  when modecol7=''TAXE'' then vts.vts_grille7 else 0 END + case  when modecol8=''TAXE'' then vts.vts_grille8 else 0 END + case  when modecol9=''TAXE'' then vts.vts_grille9 else 0 END + case  when modecol10=''TAXE'' then vts.vts_grille10 else 0 END
			,Discount=( case when modecol4=''REMISE'' then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then vts.vts_grille10 else 0 END)
			,Commission= (case when modecol4=''COMMISSION'' then vts.vts_grille4 else 0 END + case  when modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''COMMISSION'' then vts.vts_grille10 else 0 END)
			,TicketAmount=vts.vts_grille3

		FROM valeur_tarif_stock' + LTRIM(STR(@MANIFID)) + ' vts
		INNER JOIN type_tarif tt on tt.type_tarif_id = vts.type_tarif_id
		INNER JOIN categorie cat on cat.categ_id = vts.categ_id
		INNER JOIN seance s on s.seance_Id = vts.seance_id
		INNER JOIN structure on structure.structure_id > 0
		WHERE s.seance_date_fin > GETDATE() and s.seance_cloturer =''N'' and s.seance_masquer = ''N'' AND supprimer <> ''O''
		
		AND vts_v =( SELECT MAX(vts_v) FROM valeur_tarif_stock' + LTRIM(STR(@MANIFID)) + ' vts2
						WHERE vts2.tarif_logique_id=vts.tarif_logique_id
						and vts2.seance_id=vts.seance_id
						and vts2.categ_id= vts.categ_id
						and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0
		'

		print @sql

		INSERT INTO @tableSess (session_id, seance_date, price_id, price_name, category_id, category_name, amount, 
				amountHC ,charge,
				tax ,
				discount ,
				commission ,
				ticketAmount 		
		)
		EXEC (@sql)
	FETCH  NEXT FROM cursorManifs INTO @MANIFID;
	
	END

CLOSE cursorManifs;
DEALLOCATE cursorManifs;

SELECT * FROM @tableSess ORDER BY session_id, price_name, category_name