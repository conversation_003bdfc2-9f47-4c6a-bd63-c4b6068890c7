﻿
UPDATE entree_[eventID] 
 SET [dossier_id] = @pdossierid,
 dateoperation=getdate() 
,[entree_etat]='P'
WHERE entree_etat='R' and dossier_id = @pdossierid and seance_id = @psessionId

		
INSERT into entreesvg_[eventID]
 ([entree_id] 
,[seance_id] 
,[dossier_id] 
,[entree_etat] 
,[categorie_id] 
,[type_tarif_id] 
,[numero_billet] 
,[alotissement_id] 
,[reserve_id] 
,[contingent_id] 
,[montant1]
,[montant2]
,[montant3]
,[montant4]
,[montant5]
,[montant6]
,[montant7]
,[montant8]
,[montant9]
,[montant10]
,[dateoperation] 
,[dossier_v] 
,[valeur_tarif_stock_id] 
,[ValeurTarifStockVersion] )
SELECT 
entree_id,
seance_id,
dossier_id,
entree_etat,
categorie_id,
type_tarif_id,
numero_billet,
alotissement_id,
reserve_id,
contingent_id,
montant1,
montant2,
montant3,
montant4,
montant5,
montant6,
montant7,
montant8,
montant9,
montant10,
getdate(),
@pentreedossierv,
@poperateurId, /* //attention conception BIZ: operateur_id dans valeur_tarif_stock_id */
0 /* //attention conception BIZ: 0 dans ValeurTarifStockVersion */
FROM entree_[eventID] WHERE dossier_id = @pdossierid and seance_id = @psessionId
