IF COL_LENGTH('sponsor_entrees','date_push') IS NULL
	ALTER TABLE dbo.sponsor_entrees ADD date_push datetime NULL

DECLARE @activiteGroupeManif table (activite int, manif_groupe_id int)

INSERT INTO @activiteGroupeManif
SELECT preference_valeur as activite, 
CONVERT(int, replace(preference_cle, 'SPONSOR_ACTV_', '')) as manif_groupe_id
FROM structure_prefs 
WHERE preference_cle like 'SPONSOR_ACTV_%'



SELECT
se.reference_sponsor,
ag.activite,

mg.manif_groupe_id, mg.manif_groupe_nom,
s.seance_date_deb, 

*  FROM sponsor_entrees se 
INNER JOIN seance s ON s.seance_id = se.seance_id 
INNER JOIN manifestation m ON m.manifestation_id = s.manifestation_id
INNER JOIN manifestation_groupe mg ON mg.manif_groupe_id = m.manifestation_groupe_id
LEFT JOIN  @activiteGroupeManif ag ON ag.manif_groupe_id = mg.manif_groupe_id
WHERE se.date_push is null and date_operation > DATEADD(day, -3, getdate())