﻿
DECLARE @panier_produit_id_inserted INT

SELECT @panier_produit_id_inserted = scope_identity()

/* supp les produits de cette carte adhesion, de ce consumer, en etat C */
DELETE panier_produit where produit_id = @pproduitid and panier_produit_id in (
	select pp.panier_produit_id from panier_produit_carteadhesion_props prop
	INNER JOIN panier_produit pp on pp.panier_produit_id = prop.panier_produit_id 
	INNER JOIN panier p on p.panier_id = pp.panier_id  
	WHERE consumer_id = @pconsumer_id and p.etat='C'
) 

INSERT INTO panier_produit_carteadhesion_props
([panier_produit_id], [consumer_id],[adhesion_catalog_id])
VALUES (@panier_produit_id_inserted, @pconsumer_id, @padhesion_catalog_id)

