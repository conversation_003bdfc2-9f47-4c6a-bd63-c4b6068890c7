﻿---- pour organiser par date de vente/résa etc ...
CREATE table #TmpEntreeVardar2  (
	code_base varchar(250) 
	,identifiant varchar(250)
	,commande_numero varchar(250) 
	,manif_nom varchar(250)
	,lieu_nom varchar(250)
	,date_seance datetime
	,place_lib_categ varchar(250)
	,Place_lib_denom varchar(250)
	,place_montant_euros varchar(250)
	,place_montant_dg_eu varchar(250)
	--,Date_paiement varchar(250)
	--,Heure_paiement varchar(250)
	,date_paiement datetime null
	,client_nom varchar(250)
	,dossier_client_nom varchar(250)
	,commentaire varchar(250)
	,billet_etat varchar(250)
	--,Date_operation date
	--,Heure_operation time
	,entree_id int
	,rang varchar(10), siege varchar(10), zone_name varchar(50), floor_name varchar(50), section_name varchar(50)	
	,seance_id int
	,date_operation datetime	
	,place_lib_tarif varchar(50)
	,mode_paiement varchar(50)
	,barcode varchar(50)
 )

DECLARE @C VARCHAR(8000)

DECLARE @manif_id int 
DECLARE @seance_id int 


DECLARE @DateDebstr as varchar(50)
DECLARE @DateFinstr as varchar(50)
DECLARE @DateSortiestr as varchar(8)

DECLARE @DateDeb as datetime
DECLARE @DateFin as datetime 

declare @sinceMinutes int = convert(int, @psinceminutes)

IF (@sinceMinutes <>0)
BEGIN
	SET @DateDeb = (select (dateadd(n , - @sinceMinutes, getdate() )))
	SET @DateFin = getdate()
END
ELSE
BEGIN
	SET @DateDeb = @pdateFrom
	SET @DateFin = @pdateTo
END

SET @DateDebstr =convert(VARCHAR, @DateDeb, 103) + ' ' + convert(VARCHAR, @DateDeb, 108)
SET @DateFinstr = convert(VARCHAR, @datefin, 103) + ' ' + convert(VARCHAR, @datefin, 108)
SET @DateSortiestr =convert(varchar ,CONVERT(datetime,GETDATE()-1,103),112)

--select top 100 * from recette_produit order by recette_id desc
--select top 100 * from dossier_produit order by dos_prod_id desc


CREATE table #TmpEntreeVardarProduits  (
	code_base varchar(250) 
	,identifiant varchar(250)
	,commande_numero varchar(250) 
	,manif_nom varchar(250)
	,lieu_nom varchar(250)
	,date_seance datetime
	,produit_nom varchar(100)
	,produit_id int
	,nombre int
	,produit_etat varchar(10)
	,client_nom varchar(250)
	,dossier_client_nom varchar(250)
	,commentaire varchar(250)
	--,billet_etat varchar(250)
	--,Date_operation date
	--,Heure_operation time
	
	,seance_id int
	,date_operation datetime	
	,mode_paiement varchar(50)
 )

 INSERT INTO #TmpEntreeVardarProduits
SELECT 
		st.structure_nom
			,convert(varchar,dp.identite_id) +'_'+ convert(varchar,dp.dos_prod_id) + '_' + convert(varchar,dp.produit_id)  as 'Reference unique'
			,dp.commande_id as 'Numéro de commande'
			,m.manifestation_nom as 'Nom de la manifestation' 
			,lc.lieu_config_nom as 'Lieu Nom'
			,seance_date_deb
				
			,p.produit_nom
			,p.produit_id
			,dp.nb_produit
			,case dp.dos_prod_etat when  'R' then 'Réservé' when 'P' then 'Payé' when 'B' then 'Edité' when 'L' then 'Libéré' else '' end as 'Etat du produit'

			,rtrim(ltrim(i.identite_nomprenom)) 'Nom du Client'
			,rtrim(ltrim(dp.dossier_client_nom))
			,rtrim(ltrim(ci.commentaire))
			,s.seance_id
			,dp.date_operation, 
			(SELECT top 1 isnull(mp.mode_paie_nom,'ACOMPTE') from compte_client cc LEFT OUTER JOIN mode_paiement mp on mp.mode_paie_id = cc.mode_paiement_id  
				WHERE cc.cc_numpaiement = dp.num_paiement and cc.mode_paiement_id >0  and dp.num_paiement > 0  and dp.dos_prod_montant>0) as 'Mode de paiement' 			

FROM commande_ligne cl 
INNER JOIN [structure] st on st.structure_id =st.structure_id
inner join dossier_produit dp on dp.dos_prod_id = cl.dossier_id
INNER JOIN identite i on i.identite_id = dp.identite_id
inner join produit p on p.produit_id = dp.produit_id
LEFT JOIN commande_infos ci ON ci.commande_id = cl.commande_id and ci.commande_id = cl.commande_id
left join seance s on s.seance_id = dp.seance_id
left JOIN lieu_configuration lc on lc.lieu_config_id = s.lieu_config_id
left join manifestation m on m.manifestation_id = s.manifestation_id
INNER JOIN Commande_Ligne_comp clc ON clc.commande_ligne_id = cl.commande_ligne_id 

WHERE cl.type_ligne ='PRO' and dp.date_operation BETWEEN @DateDebstr and @DateFinstr 

SELECT * from #TmpEntreeVardarProduits

DROP TABLE #TmpEntreeVardarProduits

SELECT @DateDeb as dateFrom, @dateFin as dateTo