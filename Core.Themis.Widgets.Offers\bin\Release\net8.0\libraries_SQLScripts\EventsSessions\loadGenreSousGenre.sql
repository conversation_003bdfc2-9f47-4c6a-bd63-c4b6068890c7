/*
declare @pLangcode varchar(50) = 'fr'
*/

IF OBJECT_ID('dbo.traduction_manifestation_genre') IS  NULL 
BEGIN
    
    select *, null as id, null as langue_id, null as nom, null as id, null as langue_id, null as nom
    from manifestation_genre mg
    left outer join manifestation_groupe_genre mgg on mg.groupe_id = mgg.id
    ORDER by mg.code
END


IF OBJECT_ID('dbo.traduction_manifestation_genre') IS  NOT NULL 
BEGIN
    

      declare @langCode varchar(max)
    set @langCode=@pLangcode


    DECLARE @langue_id int = (select langue_id from langue where langue_code = @langCode)

    select *
    from manifestation_groupe_genre mgg
    left outer join manifestation_genre mg on mg.groupe_id = mgg.id
    LEFT JOIN traduction_manifestation_groupe_genre trad_manif_groupegenre on mg.id = trad_manif_groupegenre.id  and trad_manif_groupegenre.langue_id = @langue_id
    LEFT JOIN traduction_manifestation_genre trad_manif_genre on mg.id = trad_manif_genre.id and trad_manif_genre.langue_id = @langue_id
        
END


/*
declare @plangCode varchar(50) = 'fr'


IF OBJECT_ID('dbo.traduction_manifestation_genre') IS  NULL 
BEGIN
    
    select mg.id as sousgenre_id, mg.nom as sousgenre_nom, mg.code as sousgenre_code, 
    mgg.id as genre_id, mgg.nom as genre_nom
    from manifestation_genre mg
    left outer join manifestation_groupe_genre mgg on mg.groupe_id = mgg.id
    ORDER by mg.code
END


IF OBJECT_ID('dbo.traduction_manifestation_genre') IS  NOT NULL 
BEGIN

    declare @langCode varchar(max)
    set @langCode=@plangCode


    DECLARE @langue_id int = (select langue_id from langue where langue_code = @langCode)

    select mg.id as sousgenre_id, mg.nom as sousgenre_nom, mg.code as sousgenre_code, 
    mgg.id as genre_id, mgg.nom as genre_nom
    from manifestation_genre mg
    left outer join manifestation_groupe_genre mgg on mg.groupe_id = mgg.id
    LEFT JOIN traduction_manifestation_genre trad_manif_genre on mg.id = trad_manif_genre.id and trad_manif_genre.langue_id = @langue_id
    LEFT JOIN traduction_manifestation_groupe_genre trad_manif_groupegenre on mg.id = trad_manif_groupegenre.id   and trad_manif_groupegenre.langue_id = @langue_id
  
    ORDER by mg.code

END
*/
