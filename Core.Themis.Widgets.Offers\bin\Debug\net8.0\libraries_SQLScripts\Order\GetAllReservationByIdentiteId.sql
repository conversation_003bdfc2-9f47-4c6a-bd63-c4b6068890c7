﻿--DECLARE @pIdentiteId INT = 2

DECLARE @SqlForDossier VARCHAR(4000)
DECLARE @dossierId INT, @manifId INT

/* Définission des infos de commandes pour le fetch*/
DECLARE CommandeLigneInfos CURSOR SCROLL FOR 

	SELECT cl.dossier_id, cl.manifestation_id
	FROM commande c 
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	WHERE  cl.type_ligne ='DOS' 
	AND (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId)

OPEN CommandeLigneInfos; 
FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId;

/* Création et remplissage de la table #Dossier*/
CREATE TABLE #Dossier (
	manifestation_id_dossier INT,
	dossier_id INT,
	identite_id INT, 
	commande_id INT,
	abon_manif_id INT,
	seance_id INT, 
	dossier_v INT,
	operateur_id INT,
	dossier_montant DECIMAL(18,10),
	dossier_montant1 DECIMAL(18,10),
	dossier_montant2 DECIMAL(18,10),
	dossier_montant3 DECIMAL(18,10),
	dossier_montant4 DECIMAL(18,10),
	dossier_montant5 DECIMAL(18,10),
	dossier_montant6 DECIMAL(18,10),
	dossier_montant7 DECIMAL(18,10),
	dossier_montant8 DECIMAL(18,10),
	dossier_montant9 DECIMAL(18,10),
	dossier_montant10 DECIMAL(18,10),
	dossier_c CHAR(50),
	dossier_etat VARCHAR(1),
	dossier_icone INT,
	dossier_numero INT,
	dossier_nbplace INT,
	dossier_montantpayer DECIMAL(18,10),
	dossier_facture INT,
	dossier_client_nom CHAR(40),
	num_paiement INT,
	date_operation DATETIME,
	operation_id INT,
	filiere_id INT
)

/* Remplissage des tables temporaire*/
WHILE @@FETCH_STATUS=0 
BEGIN 
	SET @SqlForDossier = 
	'INSERT INTO #Dossier 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_dossier, d.*
		FROM dossier_' + LTRIM(STR(@manifId)) + ' d
		WHERE d.dossier_id = ' + LTRIM(STR(@dossierId))

	PRINT(@SqlForDossier)

	EXEC(@SqlForDossier)

FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId; 

END

CLOSE CommandeLigneInfos; 
DEALLOCATE CommandeLigneInfos;
	 
-- Récupérer les etats des produits (hors produit résa group 12)

SELECT DISTINCT c.* 
FROM commande c 
INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
WHERE (c.identite_id = @pIdentiteId OR cl.identite_id = @pIdentiteId)
-- 1) A un produit resa à l'état payé
AND (SELECT TOP(1) dp.dos_prod_etat 
	 FROM dossier_produit dp
	 INNER JOIN produit p ON dp.produit_id = p.produit_id
	 WHERE dp.commande_id = c.commande_id
	 AND groupe_id = 12) = 'P'
-- 2) Les produits et dossiers (hors produit résa) ne doivent pas tous être annulés
AND 'A' != ALL (SELECT d.dossier_etat COLLATE French_CI_AS AS etat
				FROM #Dossier d 
				WHERE d.commande_id = c.commande_id
				UNION
				SELECT dp.dos_prod_etat COLLATE French_CI_AS AS etat
				FROM dossier_produit dp
				INNER JOIN produit p ON dp.produit_id = p.produit_id
				WHERE dp.commande_id = c.commande_id
				AND p.groupe_id != 12)
-- 3) Les produits et dossiers doivent contenir au moins un en etat Reservé
AND 'R' = ANY (SELECT d.dossier_etat COLLATE French_CI_AS AS etat
			   FROM #Dossier d 
			   WHERE d.commande_id = c.commande_id
			   UNION
			   SELECT dp.dos_prod_etat COLLATE French_CI_AS  AS etat
			   FROM dossier_produit dp
			   INNER JOIN produit p ON dp.produit_id = p.produit_id
			   WHERE dp.commande_id = c.commande_id)
-- 4) dossier_produit_reservation ne doit pas exister donc résa guichet 
-- sinon la date limite ne doit pas être > GETDATE()
AND (
	NOT EXISTS (SELECT(1) dpr
				FROM dossier_produit dp
				INNER JOIN dossier_produit_reservation dpr ON dpr.dos_prod_id = dp.dos_prod_id
				WHERE dp.commande_id = c.commande_id)
	OR (SELECT TOP(1) dpr.date_limite
		FROM dossier_produit dp
		INNER JOIN dossier_produit_reservation dpr ON dpr.dos_prod_id = dp.dos_prod_id
		WHERE dp.commande_id = c.commande_id) > GETDATE()
)

/*Suppression des tables temporaires*/
DROP TABLE #Dossier