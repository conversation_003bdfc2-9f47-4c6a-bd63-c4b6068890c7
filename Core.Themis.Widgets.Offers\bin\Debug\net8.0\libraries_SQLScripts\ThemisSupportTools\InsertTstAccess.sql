﻿DECLARE @cntRow INT = (
					SELECT COUNT(*) FROM tst_access WHERE role_id = @pRoleId AND module_id = @pModuleId 
					AND active_directory_group_id = @pActiveDirectoryGroupId
				)

IF @cntRow = 0 
BEGIN
	INSERT INTO tst_access VALUES (@pActiveDirectoryGroupId, @pModuleId, @pRoleId); 
END


SELECT * FROM tst_access WHERE role_id = @pRoleId AND module_id = @pModuleId AND active_directory_group_id = @pActiveDirectoryGroupId

