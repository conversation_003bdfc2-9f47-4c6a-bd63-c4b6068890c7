/*
DECLARE @pEventsTypesId varchar(max) = '0'
DECLARE @pLangCode varchar(5) = 'fr'
*/

DECLARE @langueId int = (SELECT langue_id FROM langue where langue_code = @pLangCode)

SELECT DISTINCT mgr.manif_groupe_id as Id, ISNULL(tmg.manif_groupe_nom, mgr.manif_groupe_nom) AS Name 
FROM manifestation m 
INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id
LEFT OUTER JOIN traduction_manifestation_groupe tmg ON tmg.manif_groupe_id = mgr.manif_groupe_id AND langue_id = @langueId
WHERE mgr.supprimer = 'N'
AND mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
AND GETDATE() < ANY (SELECT s.seance_date_deb FROM seance s WHERE s.manifestation_id = m.manifestation_id)