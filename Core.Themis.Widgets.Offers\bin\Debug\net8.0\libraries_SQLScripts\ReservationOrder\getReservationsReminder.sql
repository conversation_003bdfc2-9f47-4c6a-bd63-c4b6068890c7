﻿  /* getReservationsReminder */
  /* liste des reservation faites qui sont toujours en R */
  SELECT ord.id, structure_id, commande_id, panier_id, etat, rem.id as rappelId,
	date_envoi, date_envoi_done from OrdersReservation ord 
 LEFT JOIN OrdersReservationReminders rem on rem.orderReservation_id = ord.id
  WHERE etat='R' 
  ORDER BY commande_id, date_envoi desc
  --and date_envoi < GETDATE() and date_envoi_done is null