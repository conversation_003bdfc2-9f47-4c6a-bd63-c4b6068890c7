﻿
--DECLARE @peventId int = 479
--DECLARE @psessionId int = 0

DECLARE @myEvent INT = 0

DECLARE @myManifs TABLE (manifestation_id int)
IF (@peventId = 0)
BEGIN
	IF (@psessionId > 0) -- manif = 0, session > 0
	BEGIN
		INSERT INTO @myManifs 
		SELECT DISTINCT manifestation_id FROM seance s WHERE s.seance_Id = @psessionId
	END
	ELSE
	BEGIN
		INSERT INTO @myManifs  -- manif = 0, session = 0 = tout
		SELECT DISTINCT manifestation_id FROM manifestation
	END
	
END
ELSE
BEGIN
	INSERT INTO @myManifs VALUES (@peventId) -- manif > 0
END

CREATE TABLE #mySessions (session_id int)
IF (@psessionId = 0)
BEGIN
	INSERT INTO #mySessions 
	SELECT DISTINCT seance_id  FROM seance
		INNER JOIN @myManifs m ON m.manifestation_id = seance.manifestation_id
		
END
ELSE
BEGIN
	INSERT INTO #mySessions 
	SELECT DISTINCT seance_id FROM seance
		INNER JOIN @myManifs m ON m.manifestation_id = seance.manifestation_id
		WHERE seance.seance_Id = @psessionId
END

CREATE TABLE #myResult (
--refuniq varchar(30), 
entreeId int,
laststate varchar(2),
dossierfiliereid int,
--filierenom varchar(30),
eventId int, 
--eventName varchar(50),
sessionid int,
--sessionStartDate datetime,  
categoryId int, 
--categoryName varchar(50),
priceId int, 
--priceName varchar(50), 
montant1 decimal(18,2),
montant2 decimal(18,2),
montant3 decimal(18,2),
montant4 decimal(18,2),
montant5 decimal(18,2),
montant6 decimal(18,2),
montant7 decimal(18,2),
montant8 decimal(18,2),
montant9 decimal(18,2),
montant10 decimal(18,2),
externe varchar(50),
motif varchar(50),
--codebarre varchar(50),
rang varchar(10), siege varchar(10), 
denomination_id int,
--denom varchar(20), 
zone_id int, etage_id int, section_id int,
--lieunom varchar(50),
commandeid int,
paymentmethodename varchar(50),
dateoperation datetime,
identiteid int,
--identitenom varchar(50),
--identiteprenom varchar(50),
--appellation varchar(50),
--postal_cp varchar(50),
--postal_ville varchar(50),
--postal_pays varchar(50),
--datenaissance datetime,
--filiere_nom varchar(20)--,
--filiereidentite int,
--postal_tel5  varchar(50), postal_tel6  varchar(50), postal_tel7  varchar(50),
--postal_tel3  varchar(50),
--postal_rue1  varchar(100),
--OPT_IN int
)


DECLARE db_cursor CURSOR FOR

SELECT DISTINCT manifestation_id
FROM @myManifs
GROUP BY manifestation_id

OPEN db_cursor   
FETCH NEXT FROM db_cursor INTO @myEvent

WHILE @@FETCH_STATUS = 0   
BEGIN 

DECLARE @sql varchar(5000)
   SET @sql = 'SELECT DISTINCT
			e.entree_id
			,e.entree_etat 
			,d.filiere_id 
			--,f.filiere_nom 
			,cl.manifestation_id 
			--,m.manifestation_nom 
			--,mg.manif_groupe_nom 
			--,NULLIF(mgg.nom,'')
			--,NULLIF(gen.nom,'')
			--,NULLIF(cbl.nom,'')
			,d.seance_id
			--,s.seance_date_deb
			,e.categorie_id 
			--,cat.categ_nom  
			,e.type_tarif_id 
			--,tt.type_tarif_nom 

			,e.montant1 
			,e.montant2 
			,e.montant3 
			,e.montant4 
			,e.montant5 
			,e.montant6 
			,e.montant7 
			,e.montant8 
			,e.montant9
			,e.montant10

			,r.externe 
			,r.motif
			--,trim(trim(r.externe) + '' '' + trim(r.motif)), 

			,rang, siege, 

			rlp.denomination_id, 
			rlp.zone_id, rlp.etage_id, rlp.section_id,
			--z.zone_nom, et.etage_nom, sect.section_nom, 

			--l.lieu_nom,
			d.commande_id
			,(select top 1 isnull(mp.mode_paie_nom,''ACOMPTE'') from compte_client cc left outer join mode_paiement mp on mp.mode_paie_id = cc.mode_paiement_id  
					where cc.cc_numpaiement = d.num_paiement and cc.mode_paiement_id >0  and d.num_paiement > 0  and d.dossier_montant>0) as modepaiement,
			e.dateoperation,
			d.identite_id 

			--,i.identite_nom 
			--,identite_prenom
			--,ga.appellation_nom
			--,i.postal_cp 
			--,i.postal_ville 
			--,i.postal_pays
			--,i.identite_date_naissance
			--,i.filiere_id

			--,case when i.postal_tel5 like ''%@%'' then i.postal_tel5 else null end as postal_tel5
			--,case when i.postal_tel6 like ''%@%'' then i.postal_tel6 else null end as postal_tel6
			--,case when i.postal_tel7 like ''%@%'' then i.postal_tel7 else null end as postal_tel7,
			--i.postal_tel3,
			--i.postal_rue1
			--,(select count(*) from identite_infos_comp iic inner join info_comp ic on ic.info_comp_id = iic.info_comp_id  where  iic.identite_id = i.identite_id 
				-- and iic.info_comp_id in (12,293) )

		FROM ENTREE_' + CONVERT(varchar,@myEvent) + ' e 
	
		INNER JOIN dossier_' + CONVERT(varchar,@myEvent) + ' d on d.dossier_id=e.dossier_id
		--INNER JOIN categorie cat on cat.categ_id = e.categorie_id
		--INNER JOIN type_tarif tt on tt.type_tarif_id = e.type_tarif_id

		INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id  = e.reference_unique_physique_id 
		INNER JOIN section sc on sc.section_id = rlp.section_id

		--INNER JOIN zone z on z.zone_id = rlp.zone_id 
		--INNER JOIN etage et on et.etage_id = rlp.etage_id 
		--INNER JOIN section sect on sect.section_id = rlp.section_id
        --INNER JOIN denomination denom on denom.denom_id = rlp.denomination_id
	--	INNER JOIN seance s on s.seance_id=d.seance_id
		INNER JOIN #mySessions mys ON mys.session_id = e.seance_id
	--	INNER JOIN tva tva ON tva.tva_id=s.taux_tva1_id
	--	INNER JOIN lieu l on l.lieu_id= s.lieu_id
		INNER JOIN commande_ligne cl on cl.dossier_id = e.dossier_id and cl.seance_id = e.seance_id and cl.type_ligne = ''DOS''
		INNER JOIN Commande_Ligne_comp clc ON clc.commande_ligne_id = cl.commande_ligne_id 
		--INNER JOIN filiere f on f.filiere_id = clc.filiere_id
		INNER JOIN manifestation m on m.manifestation_id = cl.manifestation_id
		--INNER JOIN identite i on i.identite_id = d.identite_id
		--LEFT OUTER join global_appellation ga on ga.appellation_id = i.appellation_id
		--INNER JOIN type_tarif_groupe ttg on ttg.type_tarif_groupe_id = tt.type_tarif_groupe
		LEFT OUTER join formule_abonnement fa on fa.form_abon_id = cl.formule_id
		LEFT OUTER join recette r on r.entree_id = e.entree_id and r.seance_id = e.seance_id and r.dossier_id = e.dossier_id
				
		INNER JOIN manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		LEFT OUTER JOIN super_groupe sg on sg.id_super_groupe = mg.SUPER_GROUPE_ID
		INNER JOIN [structure] st on st.structure_id =st.structure_id
		LEFT OUTER JOIN manifestation_genre gen on gen.id = m.ID_genre  --- Attention ceci est le sous-genre
		LEFT OUTER JOIN manifestation_groupe_genre mgg on mgg.id = gen.groupe_id --- Attention ceci est le genre
		--LEFT OUTER JOIN Seance_Cible scbl on scbl.Seance_id = s.seance_Id
		--LEFT OUTER JOIN cible cbl on cbl.id = scbl.Cible_id

		--LEFT OUTER JOIN filiere f2 on f2.filiere_id = i.filiere_id

		WHERE entree_etat in (''B'',''P'',''R'',''L'')
		'

		--exec (@sql)
		print @sql
			 INSERT INTO #myResult 
		exec (@sql)

   FETCH NEXT FROM db_cursor INTO @myEvent
END
CLOSE db_cursor
DEALLOCATE db_cursor
DROP TABLE #mySessions

SELECT 

CONVERT(varchar, r.identiteid) + '_' + CONVERT(varchar, r.eventId) + '_' + CONVERT(varchar, r.entreeId) as refuniq,
r.laststate,
r.dossierfiliereid as filiereid,
f.filiere_nom as filierename,
r.eventId,
m.manifestation_nom as eventname,
convert(int,s.seance_Id) AS sessionid,
s.seance_date_deb AS sessionStartDate

,mg.manif_groupe_nom as eventGroup
,NULLIF(mgg.nom,'') as eventGenre
,NULLIF(gen.nom,'') as eventSGenre,

l.lieu_id AS placeid,
l.lieu_nom as place_name,
tt.type_tarif_id as priceid,
tt.type_tarif_nom as pricename,
cat.categ_id as categoryid,
cat.categ_nom as categoryname,

z.zone_nom As zonename,
et.etage_nom AS floorname,
sect.section_nom As section_nom,
r.montant1 as amount1,
r.montant2 as amount2,
r.montant3 as amount3,
r.montant4 as amount4,
r.montant5 as amount5,
r.montant6 as amount6,
r.montant7 as amount7,
r.montant8 as amount8,
r.montant9 as amount9,
r.montant10 as amount10,


tva1.tva_libelle ,
tva1.tva_taux ,
tva2.tva_libelle as tva_amount2_lib,
tva2.tva_taux as tva_amount2_tx,

trim(r.motif) as motif,
trim(r.externe) as externe,
trim(trim(r.externe) + ' ' + trim(r.motif)) as barcode,

denom.denom_nom as denoName,
r.commandeid as orderid,
r.paymentmethodename,
r.dateoperation as operationDate,

r.identiteid as identityid,
i.identite_nom as name,
identite_prenom as surname,
ga.appellation_nom as designation,
i.postal_rue1 as address,
i.postal_cp as zipcode,
i.postal_ville as city,
i.postal_pays as country,
			
case when i.postal_tel5 like '%@%' then i.postal_tel5 else null end as phone5 ,
case when i.postal_tel6 like '%@%' then i.postal_tel6 else null end as phone6,
case when i.postal_tel7 like '%@%' then i.postal_tel7 else null end as phone7,

case when i.postal_tel5 like '%@%' then i.postal_tel5 else 
	case when i.postal_tel6 like '%@%' then i.postal_tel6 else 
		case when i.postal_tel7 like '%@%' then i.postal_tel7 else null
		end
	end
 end as email,


i.postal_tel3 as cellphone,
i.identite_date_naissance as birthdate,

f2.filiere_nom as identityfiliere,

(select count(*) from identite_infos_comp iic inner join info_comp ic on ic.info_comp_id = iic.info_comp_id  where  iic.identite_id = i.identite_id 
				 and iic.info_comp_id in (12,293) ) as opt_in

FROM #myResult r

INNER JOIN manifestation m on m.manifestation_id = r.eventId
INNER JOIN manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
LEFT OUTER JOIN super_groupe sg on sg.id_super_groupe = mg.SUPER_GROUPE_ID
LEFT OUTER JOIN manifestation_genre gen on gen.id = m.ID_genre  --- Attention ceci est le sous-genre
LEFT OUTER JOIN manifestation_groupe_genre mgg on mgg.id = gen.groupe_id --- Attention ceci est le genre

INNER JOIN seance s on s.seance_Id = r.sessionid
INNER JOIN tva tva1 ON tva1.tva_id=s.taux_tva1_id
LEFT join tva tva2 ON tva2.tva_id=s.taux_tva2_id
INNER JOIN lieu l on l.lieu_id= s.lieu_id
INNER JOIN type_tarif tt on tt.type_tarif_id = r.priceId
INNER JOIN type_tarif_groupe ttg on ttg.type_tarif_groupe_id = tt.type_tarif_groupe
INNER JOIN categorie cat on cat.categ_id = r.categoryId
INNER JOIN zone z on z.zone_id = r.zone_id 
INNER JOIN etage et on et.etage_id = r.etage_id 
INNER JOIN section sect on sect.section_id = r.section_id
INNER JOIN denomination denom on denom.denom_id = r.denomination_id

INNER JOIN identite i on i.identite_id = r.identiteid
LEFT OUTER JOIN global_appellation ga on ga.appellation_id = i.appellation_id
INNER JOIN filiere f on f.filiere_id = r.dossierfiliereid
LEFT OUTER JOIN filiere f2 on f2.filiere_id = i.filiere_id

--LEFT OUTER 

ORDER BY r.eventId, r.sessionId, r.dateoperation, r.entreeId

DROP TABLE #myResult
