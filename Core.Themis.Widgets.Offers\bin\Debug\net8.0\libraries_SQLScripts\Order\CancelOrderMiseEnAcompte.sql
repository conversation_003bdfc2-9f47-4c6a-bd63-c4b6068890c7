
/*
declare @pOrderId int = 174511
declare @poperatorId int = 5880001
declare @pposteId int = 1

*/
declare @myCptPaiement int

declare @myIdentiteIdPayeur int

-- select @myIdentiteIdPayeur = identite_id FROM commande WHERE commande_id = @pOrderId
select @myIdentiteIdPayeur = identite_id from COMPTE_CLIENT where cc_intitule_operation ='CREDITDOS' and commande_id = @pOrderId

declare @myOperateurPayeur int
select @myOperateurPayeur =  ct.operateur_id from compte_client cc 
inner join compte_transaction ct on ct.operation1_id = cc.cc_operation_id 
where cc.commande_id = @pOrderId and cc_intitule_operation = 'CREDITDOS'
order by cc_operation_id desc
if @myOperateurPayeur is not null
begin
	set @poperatorId = @myOperateurPayeur
end

DECLARE @mttotal DECIMAL(18,10)

SELECT @mttotal = SUM(cc_debit) from compte_client WHERE commande_id =@pOrderId AND cc_intitule_operation in ('DEBITDOS','DEBITPRO')


UPDATE CPT_PAIEMENT  set compteur = compteur +1;
SELECT @myCptPaiement = compteur from CPT_PAIEMENT

declare @myCptTransactionMACPTANNU int

UPDATE CPT_TRANSACTION  set compteur = compteur +1;
SELECT @myCptTransactionMACPTANNU = compteur from CPT_TRANSACTION	



declare @commande_ligne_done table (commande_ligne_id int, done int)
--declare @commande_ligne_done table (commande_ligne_id int, done int)
insert into @commande_ligne_done (commande_ligne_id, done)
select commande_ligne_id, 0 as done from commande_ligne where commande_id = @porderId and type_ligne='DOS'


declare @myCmdLigneId int
SELECT  top 1 @myCmdLigneId = commande_ligne_id from @commande_ligne_done where done =0
WHILE @myCmdLigneId >0 /* *********** for each dossier */
BEGIN


	declare @myEventId int, @mySessionId int, @myDossierId int
	select @myEventId = manifestation_id, @mySessionId = seance_id, @myDossierId = dossier_id 
	from commande_ligne where commande_ligne_id = @myCmdLigneId
	
	select @myEventId

	declare @sqlUpdateDossier nvarchar(max)

	set @sqlUpdateDossier = 'UPDATE DOSSIER_[EVENTID] 
	SET 
	dossier_v=4, 
	operateur_id=@poperatorId, 
	dossier_c='''', 
	dossier_etat=''A'', 
	dossier_icone=4097, 
	dossier_montantpayer=0, 
	dossier_facture=0, 
	dossier_client_nom='''', 
	num_paiement=@myCptPaiement, 
	date_operation=GETDATE(), 
	operation_id=0
	WHERE dossier_id = @pdossierid'
	
	set @sqlUpdateDossier = REPLACE(@sqlUpdateDossier, '[EVENTID]', @myEventId)
	
	EXEC sp_executesql @sqlUpdateDossier, N'@poperatorId int,@myCptPaiement int, @pdossierid int',@poperatorId=@poperatorId,@myCptPaiement=@myCptPaiement, @pdossierid = @myDossierId 

	declare @sqlUpdateDossierSvg nvarchar(max)

	set @sqlUpdateDossierSvg = 'INSERT INTO dossiersvg_[EVENTID] (dossier_id, identite_id, commande_id, abon_manif_id, seance_id, dossier_v, operateur_id, dossier_montant, dossier_c, dossier_etat, dossier_icone, dossier_numero, 
	dossier_nbplace, dossier_montantpayer, dossier_facture, dossier_client_nom, num_paiement, date_operation, filiere_id, type_operation, dossier_montant1, dossier_montant2, dossier_montant3, 
	dossier_montant4, dossier_montant5, dossier_montant6, dossier_montant7, dossier_montant8, dossier_montant9, dossier_montant10) 
	SELECT dossier_id, identite_id, commande_id, abon_manif_id, 
	seance_id, dossier_v, operateur_id, dossier_montant, dossier_c, dossier_etat, dossier_icone, dossier_numero, dossier_nbplace, dossier_montantpayer, dossier_facture, dossier_client_nom, 
	num_paiement, date_operation, filiere_id, ''REMB'', 
	dossier_montant1, dossier_montant2, dossier_montant3, dossier_montant4, dossier_montant5, dossier_montant6, dossier_montant7, dossier_montant8, dossier_montant9, dossier_montant10  
	FROM dossier_[EVENTID] WHERE dossier_id = @pdossierid

	INSERT INTO histodossier (date_operation, operateur_id, manif_id, seance_id, dossier_id, etat, commande_id, dossier_v, type) 
	SELECT date_operation, operateur_id, @pManifId, seance_id, dossier_id, dossier_etat, commande_id, dossier_v, ''DOS'' FROM dossier_[EVENTID] WHERE dossier_id = @pdossierid;
	'
	
	set @sqlUpdateDossierSvg = REPLACE(@sqlUpdateDossierSvg, '[EVENTID]', @myEventId)

	EXEC sp_executesql @sqlUpdateDossierSvg, N'@poperatorId int,@myCptPaiement int, @pdossierid int, @pManifId int',@poperatorId=@poperatorId,@myCptPaiement=@myCptPaiement, @pdossierid = @myDossierId, @pManifId = @myEventId

	/* foreach entree_id in dossier */

	declare @sqlGetEntrees nvarchar(max)
	
	
	set @sqlGetEntrees = '
	SELECT seance_id, entree_id, 0 FROM ENTREE_[EVENTID] 
	INNER JOIN REFERENCE_LIEU_PHYSIQUE ON entree_[EVENTID].reference_unique_physique_id = REFERENCE_LIEU_PHYSIQUE.REF_UNIQ_PHY_ID 
	WHERE DOSSIER_ID = @pdossierid AND SEANCE_ID = @pSeanceId AND ENTREE_ETAT <> ''L'' ORDER BY REFERENCE_LIEU_PHYSIQUE.SECTION_ID, 
	entree_[EVENTID].categorie_id, REFERENCE_LIEU_PHYSIQUE.ETAGE_ID, REFERENCE_LIEU_PHYSIQUE.RANG,entree_[EVENTID].entree_id'
	
	set @sqlGetEntrees = REPLACE(@sqlGetEntrees, '[EVENTID]', @myEventId)
	
	declare @tablesMyEntrees table (seance_id int, entree_id int, done int)
	
	insert into @tablesMyEntrees (seance_id, entree_id, done)
	exec sp_executesql @sqlGetEntrees, N'@pdossierid int, @pManifId int, @pSeanceId int',@pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

	declare @myEntreeId int
	select  top 1 @myEntreeId = entree_id from @tablesMyEntrees where done =0
	WHILE @myEntreeId >0 /* *********** for each entree_id in dossier */
	BEGIN
	
		--SELECT externe FROM recette Where (seance_id = @mySessionId) And (entree_id = @myEntreeId) ORDER BY recette_id DESC;

		INSERT INTO recette (manifestation_id,seance_id,entree_id,operateur_id,date_operation,type_operation,date_ouverture_caisse,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,numbillet,
		categorie_id,externe,type_tarif_id, dossier_id, motif) 		
		SELECT manifestation_id,seance_id,entree_id, @poperatorId,GETDATE(), 'A', date_ouverture_caisse,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,numbillet,
			categorie_id,externe,type_tarif_id, dossier_id, motif FROM recette
		WHERE entree_id = @myEntreeId AND dossier_id = @myDossierId AND seance_id = @mySessionId AND MANIFESTATION_ID = @myEventId AND type_operation='E'		
	
		update @tablesMyEntrees set done =1 where entree_id = @myEntreeId
		set @myEntreeId = 0
		select  top 1 @myEntreeId = entree_id from @tablesMyEntrees where done =0
		
	
	END
	
	/* end foreach entree_id in dossier */

	declare @sqlUpdateEntreeSvg nvarchar(max)

	set @sqlUpdateEntreeSvg = 'INSERT INTO entreesvg_[EVENTID] (entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, 
	montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,entree_etat,dateoperation,
	dossier_v,valeur_tarif_stock_id,valeurtarifstockversion)
	(SELECT entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,''L'',
	getdate(), 4, @poperatorId, 0 
	FROM entree_[EVENTID]  WHERE dossier_id = @pDossierId AND seance_id = @pSeanceId AND ENTREE_ETAT =''R'')'

	set @sqlUpdateEntreeSvg = REPLACE(@sqlUpdateEntreeSvg, '[EVENTID]', @myEventId)

	EXEC sp_executesql @sqlUpdateEntreeSvg, N'@poperatorId int, @pdossierid int, @pManifId int, @pSeanceId int',@poperatorId =@poperatorId, @pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId
	--declare @sqlUpdateDossierSvg nvarchar(max)

	set @sqlUpdateEntreeSvg = 'INSERT INTO entreesvg_[EVENTID] (entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id,
	montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,entree_etat,dateoperation,
	dossier_v,valeur_tarif_stock_id,valeurtarifstockversion)
	(SELECT entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,''L'',
	getdate(), 4, @poperatorId, 1 
	FROM entree_[EVENTID]  WHERE dossier_id = @pDossierId AND seance_id = @pSeanceId AND ENTREE_ETAT =''P'')'
	
	set @sqlUpdateEntreeSvg = REPLACE(@sqlUpdateEntreeSvg, '[EVENTID]', @myEventId)
	
	EXEC sp_executesql @sqlUpdateEntreeSvg, N'@poperatorId int, @pdossierid int, @pManifId int, @pSeanceId int',@poperatorId =@poperatorId, @pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

	set @sqlUpdateEntreeSvg = 'INSERT INTO entreesvg_[EVENTID] (entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, 
	montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,entree_etat,dateoperation,
	dossier_v,valeur_tarif_stock_id,valeurtarifstockversion)
	(SELECT entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,
	reserve_id,contingent_id, montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,''L'',
	getdate(), 4, @poperatorId, 2  
	FROM entree_[EVENTID]  WHERE dossier_id = @pDossierId AND seance_id = @pSeanceId AND entree_etat =''B'')'
	
	set @sqlUpdateEntreeSvg = REPLACE(@sqlUpdateEntreeSvg, '[EVENTID]', @myEventId)
	
	EXEC sp_executesql @sqlUpdateEntreeSvg, N'@poperatorId int, @pdossierid int, @pManifId int, @pSeanceId int',@poperatorId =@poperatorId, @pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

	declare @sqlUpdateEntree nvarchar(max)
	
	set @sqlUpdateEntree = 'UPDATE entree_[EVENTID] SET 
	entree_etat = ''L'', 
	dossier_id = 0, 
	type_tarif_id = 0, 
	valeur_tarif_stock_id = 0, 
	flag_selection = '''',
	montant1 = 0, 
	montant2 = 0, 
	montant3 = 0, 
	montant4 = 0, 
	montant5 = 0, 
	montant6 = 0, 
	montant7 = 0, 
	montant8 = 0, 
	montant9 = 0, 
	montant10 = 0, 
	dateoperation = getdate(), 
	controleacces = null 
	WHERE DOSSIER_ID = @pDossierId AND SEANCE_ID = @pSeanceId'
	
	SET @sqlUpdateEntree = REPLACE(@sqlUpdateEntree, '[EVENTID]', @myEventId)
	
	EXEC sp_executesql @sqlUpdateEntree, N'@poperatorId int, @pdossierid int, @pManifId int, @pSeanceId int',@poperatorId =@poperatorId, @pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

	DECLARE @myCptTransactionRembDos int

	UPDATE CPT_TRANSACTION  set compteur = compteur +1;
	SELECT @myCptTransactionRembDos = compteur from CPT_TRANSACTION	

	------------> insert RembDos depuis select debitDos	
	INSERT INTO COMPTE_CLIENT (cc_operation_id,identite_id,cc_intitule_operation,cc_debit,cc_credit,cc_solde,operateur_id,cc_date_operation,mode_paiement_id,cc_banque,cc_cb, cc_numcheque, 
		manif_id,seance_id,dossier_id,cc_numpaiement,cc_typetransaction,
		cc_taux,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,commande_id,num_facture,num_acompte) 
	
	SELECT 	@myCptTransactionRembDos,@myIdentiteIdPayeur ,'REMBDOS', CC_DEBIT, CC_CREDIT,CC_SOLDE,                 @poperatorId,GETDATE(), 0,' ',' ',' ',
		@myEventId,@mySessionId,@myDossierId,@myCptPaiement,'REMBDOS', 
		1,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,commande_id,num_facture,num_acompte 
	FROM COMPTE_CLIENT where cc_intitule_operation = 'DEBITDOS' and MANIF_ID = @myEventId and SEANCE_ID=@mySessionId and DOSSIER_ID=@myDossierId
		
		--VALUES (@myCptTransactionRembDos,@myIdentiteId,'REMBDOS',52,0,0,@poperatorId,GETDATE() ,0,' ',' ',' ',@myEventId,@mySessionId,@myDossierId,@myCptPaiement,'REMBDOS',1,44.6,7.4,44.6,0,4,3.4,0,0,0,0,174511,0,0)

	INSERT INTO COMPTE_CLIENT2 (CC_OPERATION2_ID,POSTE_ID,POSTE) VALUES (@myCptTransactionRembDos,@pposteId,'THEMIS')

	DECLARE @myCptTransactionAnnulDos int

	UPDATE CPT_TRANSACTION  set compteur = compteur +1;
	SELECT @myCptTransactionAnnulDos = compteur from CPT_TRANSACTION	


	------------>  insert AnnulDos depuis select creditDos
	INSERT INTO COMPTE_CLIENT (CC_OPERATION_ID,IDENTITE_ID,CC_INTITULE_OPERATION,CC_DEBIT,CC_CREDIT,CC_SOLDE,OPERATEUR_ID,CC_DATE_OPERATION,MODE_PAIEMENT_ID,CC_BANQUE,CC_CB, CC_NUMCHEQUE,
		MANIF_ID,SEANCE_ID,DOSSIER_ID,CC_NUMPAIEMENT, CC_TYPETRANSACTION,
		CC_TAUX,MONTANT1,MONTANT2,MONTANT3,MONTANT4,MONTANT5,MONTANT6,MONTANT7,MONTANT8,MONTANT9,MONTANT10,COMMANDE_ID,NUM_FACTURE,NUM_ACOMPTE )
	
		SELECT 	@myCptTransactionAnnulDos,@myIdentiteIdPayeur ,'ANNULDOS', CC_DEBIT, CC_CREDIT,CC_SOLDE,                 @poperatorId,GETDATE(), 0,' ',' ',' ',
		@myEventId,@mySessionId,@myDossierId,@myCptPaiement,'ANNULDOS', 
		1,MONTANT1,MONTANT2,MONTANT3,MONTANT4,MONTANT5,MONTANT6,MONTANT7,MONTANT8,MONTANT9,MONTANT10,COMMANDE_ID,NUM_FACTURE,NUM_ACOMPTE 
	FROM COMPTE_CLIENT WHERE cc_intitule_operation = 'CREDITDOS' and MANIF_ID = @myEventId and SEANCE_ID=@mySessionId and DOSSIER_ID=@myDossierId

	--VALUES (@myCptTransactionAnnulDos,@myIdentiteId,'ANNULDOS',0,52,0,@poperatorId,GETDATE() ,0,' ',' ',' ',@myEventId,@mySessionId,@myDossierId,@myCptPaiement,'ANNULDOS',1,44.6,7.4,44.6,0,4,3.4,0,0,0,0,174511,0,0)
	
	INSERT INTO COMPTE_CLIENT2 (CC_OPERATION2_ID,POSTE_ID,POSTE) VALUES (@myCptTransactionAnnulDos,@pposteId,'THEMIS')

	INSERT INTO compte_transaction (operation1_id,operation2_id,transaction_c,operateur_id,transaction_date,transaction_type,montant,mode_paiement_id,num_paiement,type_dos,manifestation_id,seance_id,dossier_id,payeur_id,commande_id,num_dossier,num_facture,solde) 
	VALUES (@myCptTransactionRembDos,@myCptTransactionMACPTANNU,' ',@poperatorId, GETDATE() ,'REMB',0,0,@myCptPaiement,' ',0,0,0,0,0,0,0,0)
	
	UPDATE @commande_ligne_done set done =1 WHERE commande_ligne_id = @myCmdLigneId
	set @myCmdLigneId = 0
	SELECT top 1 @myCmdLigneId = commande_ligne_id from @commande_ligne_done where done =0

end /* ******************  end for each dossier */


--------------------------- les produits 
delete @commande_ligne_done
insert into @commande_ligne_done (commande_ligne_id, done)
select commande_ligne_id, 0 as done from commande_ligne where commande_id = @porderId and type_ligne='PRO'


SELECT  top 1 @myCmdLigneId = commande_ligne_id from @commande_ligne_done where done =0
WHILE @myCmdLigneId >0 /* *********** for each dossier */
BEGIN


	DECLARE @myDossierProdId int, @dossier_v int, @prodCount int, @prod_stock_id int
	
	SELECT @myDossierProdId = dossier_id FROM commande_ligne dp WHERE dp.commande_ligne_id = @myCmdLigneId
	
	SELECT @prodCount = nb_produit, @prod_stock_id = dos_prod_stock_id, @dossier_v = dos_prod_v FROM dossier_produit WHERE dos_prod_id = @myDossierProdId 
	
	UPDATE DOSSIER_PRODUIT SET DOS_PROD_V=@dossier_v + 1,OPERATEUR_ID=@poperatorId,
		DOS_PROD_ETAT='A',		
		NUM_PAIEMENT=@myCptPaiement,
		DATE_OPERATION=GETDATE()	
		WHERE DOS_PROD_ID = @myDossierProdId
	

	UPDATE iic set iic.supprimer = 'O' from dossier_produit dp
		 inner join identite_infos_comp iic on iic.identite_id = dp.identite_id and iic.id = dp.dos_prod_numero
		 inner join produit p on p.produit_id = dp.produit_id
		 Inner join dossier_produitsvg dpsvg on dpsvg.dos_prod_id = dp.dos_prod_id and dpsvg.dos_prod_etat = 'R' and dpsvg.dos_prod_v = 1 
		 WHERE dp.Dos_Prod_ID = @myDossierProdId and p.infocomp_id > 0 
		 and iic.info_comp_id = p.infocomp_id 
		 and iic.identite_id = dp.identite_id 
		 and convert(date,iic.datecreation) = convert(date,dpsvg.date_operation)  

	UPDATE produit_stock  
		SET restant = restant + @prodCount
		WHERE produit_stock_id = @prod_stock_id;


	Update Dossier_Produit_Entree set etat_place = 'L' ,date_operation = getdate() where Dos_prod_ID = (@myDossierProdId) and etat_place <> 'L'

	DECLARE @myCptTransactionRembPro int

	UPDATE CPT_TRANSACTION  set compteur = compteur +1;
	SELECT @myCptTransactionRembPro = compteur from CPT_TRANSACTION	

	---------> insert RembProd depuis select debitPro
	INSERT INTO compte_client (cc_operation_id,identite_id,cc_intitule_operation,cc_debit,cc_credit,cc_solde,operateur_id,cc_date_operation,mode_paiement_id,
	cc_banque,cc_cb,cc_numcheque,manif_id,seance_id,dossier_id,cc_numpaiement,cc_typetransaction,cc_taux,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,commande_id,num_facture,num_acompte) 
	SELECT 
	@myCptTransactionRembPro,identite_id,'REMBPRO',cc_debit,cc_credit,cc_solde,@poperatorId,GETDATE(),0,
	Cc_banque,cc_cb,cc_numcheque,manif_id,seance_id,dossier_id,@myCptPaiement,'REMBPRO',cc_taux,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,commande_id,num_facture,num_acompte 
	FROM compte_client 
	WHERE cc_intitule_operation = 'DEBITPRO' and dossier_id = @myDossierProdId and commande_id =@pOrderId
	
	--VALUES (@myCptTransactionRembPro,102749,'REMBPRO',0,0,0,5880001,GETDATE() ,0,' ',' ',' ',339,3491,106993,262894,'REMBPRO',1,0,0,0,0,0,0,0,0,0,0,174176,0,0)

	INSERT INTO COMPTE_CLIENT2 (CC_OPERATION2_ID,POSTE_ID,POSTE) VALUES (@myCptTransactionRembPro, @pposteId,'THEMIS')
	
		---------> insert AnnulPro depuis select creditPRO
		
	DECLARE @myCptTransactionAnnulPro int

	UPDATE cpt_transaction SET compteur = compteur +1;
	SELECT @myCptTransactionAnnulPro = compteur from CPT_TRANSACTION			
		
	INSERT INTO compte_client (cc_operation_id,identite_id,cc_intitule_operation,cc_debit,cc_credit,cc_solde,operateur_id,cc_date_operation,mode_paiement_id,
	cc_banque,cc_cb,cc_numcheque,manif_id,seance_id,dossier_id,cc_numpaiement,cc_typetransaction,cc_taux,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,commande_id,num_facture,num_acompte) 
	SELECT 
	@myCptTransactionAnnulPro,identite_id,'ANNULPRO',cc_debit,cc_credit,cc_solde,@poperatorId,GETDATE(),0,
	cc_banque,cc_cb,cc_numcheque,manif_id,seance_id,dossier_id,@myCptPaiement,'REMBPRO',cc_taux,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,commande_id,num_facture,num_acompte 
	FROM COMPTE_CLIENT 
	WHERE cc_intitule_operation = 'CREDITPRO' and dossier_id = @myDossierProdId and commande_id =@pOrderId
	
	
	INSERT INTO COMPTE_CLIENT2 (CC_OPERATION2_ID,POSTE_ID,POSTE) VALUES (@myCptTransactionAnnulPro, @pposteId,'THEMIS')
	
	INSERT INTO compte_transaction (operation1_id,operation2_id,transaction_c,operateur_id,transaction_date,transaction_type,montant,mode_paiement_id,num_paiement,type_dos,manifestation_id,seance_id,dossier_id,payeur_id,commande_id,num_dossier,num_facture,solde) 
	VALUES (@myCptTransactionRembPro,@myCptTransactionMACPTANNU,' ',@poperatorId, GETDATE() ,'REMB',0,0,@myCptPaiement,' ',0,0,0,0,0,0,0,0)

	UPDATE @commande_ligne_done set done =1 where commande_ligne_id = @myCmdLigneId
	set @myCmdLigneId = 0
	select top 1 @myCmdLigneId = commande_ligne_id from @commande_ligne_done where done =0

END


INSERT INTO COMPTE_CLIENT (CC_OPERATION_ID, IDENTITE_ID, CC_INTITULE_OPERATION, CC_DEBIT, CC_CREDIT, CC_SOLDE,OPERATEUR_ID, CC_DATE_OPERATION, MODE_PAIEMENT_ID, CC_BANQUE, CC_CB,
CC_NUMCHEQUE, MANIF_ID, SEANCE_ID, DOSSIER_ID, CC_NUMPAIEMENT, CC_TYPETRANSACTION, CC_TAUX, MONTANT1, MONTANT2, MONTANT3, MONTANT4, MONTANT5, MONTANT6, MONTANT7, MONTANT8, MONTANT9, MONTANT10, COMMANDE_ID, NUM_FACTURE, NUM_ACOMPTE) 
VALUES (@myCptTransactionMACPTANNU,@myIdentiteIdPayeur ,'MACPTANNU',0,@mttotal,@mttotal,@poperatorId,                              GETDATE() ,1,' ','VIA API',' ',0,0,0,@myCptPaiement,'MACPTANNU',1,@mttotal,0,0,0,0,0,0,0,0,0, @pOrderId  ,0,0)

INSERT INTO COMPTE_CLIENT2 (CC_OPERATION2_ID,POSTE_ID,POSTE) VALUES (@myCptTransactionMACPTANNU,@pposteId,'THEMIS')

----------------------
UPDATE IDENTITE set MONTANT_CREDIT=MONTANT_CREDIT + @mttotal, MONTANT_DEBIT=MONTANT_DEBIT+0  WHERE IDENTITE_ID= @myIdentiteIdPayeur







