﻿--DECLARE @plangCode varchar(2) = 'DE'

DECLARE @langId INT = ISNULL((SELECT langue_id FROM langue WHERE langue_code = @plangCode), 1)


SELECT fa.form_abon_id, ISNULL(tfa.form_abon_nom, fa.form_abon_nom) as form_abon_nom,
ISNULL(tfa.form_abon_code, fa.form_abon_code) as form_abon_code, fa.form_abon_manifmin,
fa.form_abon_manifmax, fa.forcer, fa.filiere_id, fa.form_abon_coefmin, fa.form_abon_coefmax, fa.masquer
FROM formule_abonnement fa
LEFT OUTER JOIN traduction_formule_abonnement tfa ON tfa.form_abon_id = fa.form_abon_id AND tfa.langue_id = @langId

