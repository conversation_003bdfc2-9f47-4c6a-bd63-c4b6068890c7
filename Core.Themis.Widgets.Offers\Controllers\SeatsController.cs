﻿using Core.Themis.Libraries.BLL.CarnetTickets.Interfaces;
using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.Structures;
using Core.Themis.Libraries.Utilities.Helpers.Sponsors;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.Models;
using Core.Themis.Widgets.Offers.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class SeatsController : Controller
    {
        private static readonly RodrigueNLogger Logger = new();

        private readonly IConfiguration _configuration;
        private readonly IBasketManager _basketManager;
        private readonly IGestionTraceManager _gestionTrace;
        public readonly ISponsorManager _sponsorManager;
        public readonly IFeedBooksManager _feedBooksManager;
        public readonly IFeedBookTokensManager _feedBookTokensManager;
        public readonly IReservesManager _reservesManager;
        public readonly IPriceManager _pricesManager;
        public readonly ISeatManager _seatManager;
        private readonly IStructurePrefsManager _structurePrefsManager;
        private readonly IIdentiteManager _identiteManager;

        public SeatsController(
            IConfiguration configuration,
            IBasketManager basketManager,
            IGestionTraceManager gestionTrace,
            ISponsorManager sponsorManager,
            IFeedBooksManager feedBooksManager,
            IFeedBookTokensManager feedBookTokensManager,
            IReservesManager reservesManager,
            IPriceManager priceManager,
            ISeatManager seatManager,
            IStructurePrefsManager structurePrefsManager,
            IIdentiteManager identiteManager)
        {
            _configuration = configuration;
            _basketManager = basketManager;
            _gestionTrace = gestionTrace;
            _sponsorManager = sponsorManager;
            _feedBooksManager = feedBooksManager;
            _feedBookTokensManager = feedBookTokensManager;
            _reservesManager = reservesManager;
            _pricesManager = priceManager;
            _seatManager = seatManager;
            _structurePrefsManager = structurePrefsManager;
            _identiteManager = identiteManager;
        }




        /// <summary>
        /// Flag AUTO
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="token"></param>
        /// <param name="langCode"></param>
        /// <param name="propertiesSeatsSelectionLocationsToFlag"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{structureId}/FlagAjax/{eventId}/{sessionId}/{identityId}/{basketId}/{webUserId}/{buyerProfilId}/{langCode}")]
        public async Task<IActionResult> FlagAjax(int structureId, int eventId, int sessionId, int identityId, int basketId, int webUserId, int buyerProfilId, string token, string langCode, PropertiesSeatsSelectionLocationToFlag[] propertiesSeatsSelectionLocationsToFlag)
        {
            Logger.Info(structureId, $"FlagAjax ({structureId}, {eventId}, {sessionId}, {identityId}, {webUserId}, {buyerProfilId}, {propertiesSeatsSelectionLocationsToFlag.Length})");
            string typeRun = _configuration["TypeRun"]!;

            int nbSeatsToTake = propertiesSeatsSelectionLocationsToFlag.SelectMany(prop => prop.PropertiesSeatSelectionToFlagList.Select(propseat => propseat.SeatCount)).FirstOrDefault();
            int getionPlaceId = propertiesSeatsSelectionLocationsToFlag.SelectMany(prop => prop.PropertiesSeatSelectionToFlagList.Select(propseat => propseat.GestionPlaceId)).FirstOrDefault();

            BasketDTO bask = new();
            List<SeatDTO> allSeats = new();
            List<EventDTO> listEvent = new List<EventDTO>();
            try
            {
                // la liste de tarifs, on passe basketId = 0 car pas besoin de recalculer max / min
                listEvent = _pricesManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, "", "", 0);

            }
            catch (Exception ex)
            {
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Pb dans la récupération de la liste des tarifs manifid={eventId}, seanceid={sessionId}, identiteid={identityId},profilacheteurid={buyerProfilId}");
                throw;
            }
            bool ThereIsStrapontins = false;
            bool ThereIsPlacesDiscontigues = false;


            if (listEvent != null && listEvent.Count > 0 && listEvent[0].ListSessions != null && listEvent[0].ListSessions.Count > 0)
            {
                var zones = listEvent[0].ListSessions[0].ListZones;
                var floors = zones.SelectMany(f => f.ListFloors).ToList();
                var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();
                var gpList = categories.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Distinct().ToList();

                #region check les gp demandés % grille de tarif
                // check gp demandés % grille de tarif
                List<int> listGpApb = new List<int>();
                foreach (var seatsSelectionLocation in propertiesSeatsSelectionLocationsToFlag)
                {
                    foreach (var listfl in seatsSelectionLocation.PropertiesSeatSelectionToFlagList)
                    {
                        var listThisGp = gpList.Where(gp => gp.GestionPlaceId == listfl.GestionPlaceId);
                        if (listThisGp == null || listThisGp.Count() == 0)
                        {
                            // ne retrouve pas dans la grille de tarif relue le gpId demandé en parametre !!?
                            // ex: l'internaute s'est logé et n'a plus droit au tarif qu'il a choisi
                            listGpApb.Add(listfl.GestionPlaceId);
                        }
                    }
                }
                if (listGpApb != null && listGpApb.Count > 0)
                {
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"certains tarifs ne sont plus ok, pete une erreur");
                    // certains tarifs ne sont plus ok, pete une erreur :
                    Logger.Error(structureId, $"FlagAjax/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{langCode},{JsonConvert.SerializeObject(propertiesSeatsSelectionLocationsToFlag)}) listThisGp is null !");
                    return Problem($"gps:{string.Join(',', listGpApb)}", null, 401, "priceGrid:RightsOnPricesError");
                }

                #endregion


                //Ajoute les consommateurs au pool
                var identitiesForAddToPool = propertiesSeatsSelectionLocationsToFlag.SelectMany(prop => prop.PropertiesSeatSelectionToFlagList.SelectMany(u => u.ListIdentities)).Distinct().ToList();

                foreach (var consumer in identitiesForAddToPool)
                {
                    if (!string.IsNullOrEmpty(consumer))
                    {
                        int iConsumer = int.Parse(consumer);
                        if (iConsumer > 0)
                            _identiteManager.LinkConsumer(structureId, identityId, iConsumer);

                    }

                }
                bool isPlacementlibre = false;

                foreach (var seatsSelectionLocation in propertiesSeatsSelectionLocationsToFlag)
                {
                    allSeats = new List<SeatDTO>();

                    var distinctGpIds = seatsSelectionLocation.PropertiesSeatSelectionToFlagList.Select(gp => gp.GestionPlaceId).Distinct().ToList();

                    //List<ReserveDTO> listCommonReserves = await ApiOffersHelper.GetListReservesCommons(apiOffersUrl, token, structureId, distinctGpIds);

                    List<int> listCommonReservesIds = _reservesManager.LoadCommonsReservesListForGpIds(structureId, distinctGpIds);


                    if (listCommonReservesIds == null || listCommonReservesIds.Count == 0) ////////////////// ** pas de reserves communes, gestion place par gestion place
                    {
                        bool isContigue = false; // les places seront forcement discontigues

                        int seatAttribue = 0;
                        int cntSponsorUsedCard = 0;

                        foreach (var listfl in seatsSelectionLocation.PropertiesSeatSelectionToFlagList)
                        {
                            int nbSeatsThisGp = listfl.SeatCount;

                            List<List<SeatDTO>> listOfListSeats = new List<List<SeatDTO>>();

                            List<int> seatsId = listfl.SeatsId;
                            if (seatsId.Count > 0)
                            {
                                allSeats = _seatManager.FlagManually(structureId, eventId, sessionId, webUserId, seatsId, "T" + webUserId, 0, 0);
                            }
                            else
                            {
                                List<int> listThesesReservesIds = _reservesManager.LoadCommonsReservesListForGpIds(structureId, new List<int> { listfl.GestionPlaceId });

                                //listOfListSeats = await ApiOffersHelper.FlagSeatsSelection(apiOffersUrl, token, structureId, eventId, sessionId, seatsSelectionLocation.ZoneId, seatsSelectionLocation.FloorId,
                                //  seatsSelectionLocation.SectionId, seatsSelectionLocation.CategId, nbSeatsThisGp, webUserId, listThesesReservesIds);

                                listOfListSeats = _seatManager.FlagAuto(structureId, nbSeatsThisGp, sessionId, seatsSelectionLocation.ZoneId, seatsSelectionLocation.FloorId, seatsSelectionLocation.SectionId, seatsSelectionLocation.CategId, listThesesReservesIds, "T" + webUserId);
                                listOfListSeats.ForEach(listSeat => allSeats.AddRange(listSeat));
                            }


                            if (listOfListSeats.Count() > 0 || allSeats.Count > 0)
                            {
                                bool isStrapontin = false;
                                try
                                {

                                    if (allSeats.Count > 0)
                                    {
                                        //si dans la liste il y au moins 1 strapontin
                                        if (allSeats.Any(s => s.TypeSeat == "SD" || s.TypeSeat == "SG"))
                                        {
                                            isStrapontin = true;
                                        }

                                        bask = _basketManager.CreateBasketIfNotExists(structureId, webUserId, identityId, "C", null);
                                        bask.Hash = bask.GetHash();



                                        //foreach (var listfl in seatsSelectionLocation.PropertiesSeatSelectionToFlagList)
                                        //{

                                        var listThisGp = gpList.Where(gp => gp.GestionPlaceId == listfl.GestionPlaceId);
                                        if (listThisGp == null || listThisGp.Count() == 0)
                                        {
                                            // ne retrouve pas dans la grille de tarif relue le gpId demandé en parametre !!?
                                            // ex: l'internaute s'est logé et n'a plus droit au tarif qu'il a choisi
                                            Logger.Error(structureId, $"FlagAjax/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{langCode},{JsonConvert.SerializeObject(propertiesSeatsSelectionLocationsToFlag)}) listThisGp is null !");
                                            return Problem($"priceGrid for {listfl.GestionPlaceId}", null, 401, "priceGridError");

                                        }
                                        var thisGp = listThisGp.FirstOrDefault();

                                        for (int i = 0; i < listfl.SeatCount; i++)
                                        {
                                            var seatFlage = allSeats[seatAttribue];

                                            int consom = 0;
                                            if (listfl.ListIdentities != null && listfl.ListIdentities.Count >= i + 1)
                                            {
                                                string sconsom = listfl.ListIdentities[i];
                                                int.TryParse(sconsom, out consom);
                                                //consom = listfl.ListIdentities.Count[i];
                                            }

                                            if (thisGp.IsPlacementLibre)
                                            {
                                                seatFlage.Rank = "LIBRE";
                                                seatFlage.Seat = "LIBRE";
                                                isPlacementlibre = true;
                                            }

                                            // *********************************************************************************
                                            // TODO ajouter l'identité eventuellement reliée ***********************************
                                            // *********************************************************************************

                                            // int basketLineId = _basketManager.AddSeat(sqlConnWT, structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], seatFlage, thisGp, consom, pathScriptSqlCommons);
                                            int basketLineId = _basketManager.AddSeatInBasketLine(structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], seatFlage, thisGp, consom);


                                            var sponsorSeat = seatsSelectionLocation.PropertiesSeatSelectionToFlagList.Where(gp => gp.GestionPlaceId == thisGp.GestionPlaceId).FirstOrDefault();


                                            try
                                            {
                                                //var oneCardHaveBeenUsed = await AddCardSponsorsAsync(structureId, basketLineId, sponsorSeat, cntSponsorUsedCard, webUserId, apiOffersUrl, token, eventId, sessionId, seatFlage.SeatId, thisGp.GestionPlaceId);
                                                var oneCardHaveBeenUsed = await _sponsorManager.AddCardSponsorsAsync(structureId, basketLineId, sponsorSeat, cntSponsorUsedCard, webUserId, eventId, sessionId, seatFlage.SeatId, thisGp.GestionPlaceId);

                                                if (oneCardHaveBeenUsed)
                                                {
                                                    cntSponsorUsedCard++;
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                if (ex.Message.Contains("|"))
                                                {
                                                    return Problem(detail: ex.Message.Split('|')[1], title: ex.Message.Split('|')[0], statusCode: StatusCodes.Status403Forbidden);
                                                }
                                            }
                        
                                            seatAttribue++;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"flagAjax {ex.Message}");
                                    throw new Exception(ex.Message);
                                }
                            }
                            else
                            {
                                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"pb dans le flag => plus assez de places");
                                return Problem(detail: "plus2place", statusCode: StatusCodes.Status401Unauthorized, title: "priceGrid:NotEnoughSeatAvailable");
                            }


                        }


                        // Logger.Error(structureId, $"FlagAjax ({structureId}, {eventId}, {sessionId}, {identityId}, {webUserId}, {buyerProfilId}, {listCommonReservesIds?.Count}): there is no commons reserves");
                    }
                    else
                    {
                        //////// seat auto ???
                        int nbTotalSeats = seatsSelectionLocation.PropertiesSeatSelectionToFlagList.Sum(x => x.SeatCount);

                        //List<int> listCommonReservesIds = listCommonReserves.Select(r => r.ReserveId).ToList();

                        List<List<SeatDTO>> listOfListSeats = new List<List<SeatDTO>>();

                        List<int> seatsId = seatsSelectionLocation.PropertiesSeatSelectionToFlagList.SelectMany(s => s.SeatsId).ToList();
                        
                        // **************************** flag sur plan
                        if (seatsId.Count > 0)
                        {
                            allSeats = _seatManager.FlagManually(structureId, eventId, sessionId, webUserId, seatsId, "T" + webUserId, 0,0);

                        }
                        else
                        {  // ************************ flag auto                            

                            listOfListSeats = _seatManager.FlagAuto(structureId, nbTotalSeats, sessionId, seatsSelectionLocation.ZoneId, seatsSelectionLocation.FloorId, seatsSelectionLocation.SectionId, seatsSelectionLocation.CategId, listCommonReservesIds, "T" + webUserId);
                            listOfListSeats.ForEach(listSeat => allSeats.AddRange(listSeat));
                        }


                        if (listOfListSeats.Count() > 0 || allSeats.Count > 0)
                        {
                            bool isContigue = true;
                            bool isStrapontin = false;
                            if (listOfListSeats.Count > 1)
                            {
                                isContigue = false;
                            }

                            /* TODO
                             * placement libre + discontinues et strapontins
                             * placement libre ne pas afficher le rang et siege
                             **/

                            try
                            {
                                if (allSeats.Count > 0)
                                {
                                    //si dans la liste il y au moins 1 strapontin
                                    if (allSeats.Any(s => s.TypeSeat == "SD" || s.TypeSeat == "SG"))
                                    {
                                        isStrapontin = true;
                                    }

                                    bask = _basketManager.CreateBasketIfNotExists(structureId, webUserId, identityId, "C", null);
                                    bask.Hash = bask.GetHash();

                                    int seatAttribue = 0;

                                    foreach (var listfl in seatsSelectionLocation.PropertiesSeatSelectionToFlagList)
                                    {
                                        int cntSponsorUsedCard = 0;
                                        int cntFeedBookToken = 0;


                                        var listThisGp = gpList.Where(gp => gp.GestionPlaceId == listfl.GestionPlaceId);
                                        if (listThisGp == null || listThisGp.Count() == 0)
                                        {
                                            // ne retrouve pas dans la grille de tarif relue le gpId demandé en parametre !!?
                                            // ex: l'internaute s'est logé et n'a plus droit au tarif qu'il a choisi
                                            Logger.Error(structureId, $"FlagAjax/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{langCode},{JsonConvert.SerializeObject(propertiesSeatsSelectionLocationsToFlag)}) listThisGp is null !");
                                            return Problem($"priceGrid for {listfl.GestionPlaceId}", null, 401, "priceGridError");

                                        }
                                        var thisGp = listThisGp.FirstOrDefault();

                                        for (int i = 0; i < listfl.SeatCount; i++)
                                        {
                                            var seatFlage = allSeats[seatAttribue];

                                            int consom = 0;
                                            if (listfl.ListIdentities != null && listfl.ListIdentities.Count >= i + 1)
                                            {
                                                string sconsom = listfl.ListIdentities[i];
                                                int.TryParse(sconsom, out consom);
                                                //consom = listfl.ListIdentities.Count[i];
                                            }





                                            if (thisGp.IsPlacementLibre)
                                            {
                                                seatFlage.Rank = "LIBRE";
                                                seatFlage.Seat = "LIBRE";
                                                isPlacementlibre = true;
                                            }

                                            // *********************************************************************************
                                            // TODO ajouter l'identité eventuellement reliée ***********************************
                                            // *********************************************************************************

                                            // int basketLineId = _basketManager.AddSeat(sqlConnWT, structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], seatFlage, thisGp, consom, pathScriptSqlCommons);
                                            int basketLineId = _basketManager.AddSeatInBasketLine(structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], seatFlage, thisGp, consom);

                                            #region sponsors
                                            var sponsorSeat = seatsSelectionLocation.PropertiesSeatSelectionToFlagList.Where(gp => gp.GestionPlaceId == thisGp.GestionPlaceId).FirstOrDefault();
                                            try
                                            {
                                                var oneCardHaveBeenUsed = await _sponsorManager.AddCardSponsorsAsync(structureId, basketLineId, sponsorSeat, cntSponsorUsedCard, webUserId, eventId, sessionId, seatFlage.SeatId, thisGp.GestionPlaceId);

                                                if (oneCardHaveBeenUsed)
                                                {
                                                    cntSponsorUsedCard++;
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                if (ex.Message.Contains("|"))
                                                {
                                                    return Problem(detail: ex.Message.Split('|')[1], title: ex.Message.Split('|')[0], statusCode: StatusCodes.Status403Forbidden);
                                                }
                                            }
                                            #endregion

                                            #region FeedBooks

                                            if (listfl.FeedBookTokensUsed != null && listfl.FeedBookTokensUsed.Count >= i + 1)
                                            {
                                                int thisFeedBookTokenId = listfl.FeedBookTokensUsed[i].TokenId;
                                                string thisFeedBookTokenIdentifiant = listfl.FeedBookTokensUsed[i].Identifiant;

                                             //   var feedBookSeat = seatsSelectionLocation.PropertiesSeatSelectionToFlagList.Where(gp => gp.GestionPlaceId == thisGp.GestionPlaceId).FirstOrDefault();
                                                try
                                                {
                                                    var oneTokenBeenUsed = await _feedBookTokensManager.AddFeedBookTokenAsync(structureId, basketLineId, thisFeedBookTokenId, thisFeedBookTokenIdentifiant);

                                                }
                                                catch (Exception ex)
                                                {
                                                    Logger.Error(structureId, $"FlagAjax region FeedBooks AddFeedBookTokenAsync : {ex.Message} {ex.StackTrace}");
                                                    throw;
                                
                                                }
                                            }
                                            #endregion

                                            seatAttribue++;
                                        }

                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"flagAjax {ex.Message}");

                                throw new Exception(ex.Message);
                            }


                            if (!isPlacementlibre)
                            {
                                if (!isContigue && isStrapontin)
                                {
                                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Les places ne sont pas contigues et il y a des strapontins");
                                }

                                if (!isContigue)
                                {
                                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Les places ne sont pas contigues");
                                    ThereIsPlacesDiscontigues = true;
                                }

                                if (isStrapontin)
                                {
                                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Places sur strapontins");
                                    ThereIsStrapontins = true;
                                }
                            }
                        }
                        else
                        {
                            _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"pb dans le flag => plus assez de places");
                            return Problem(detail: "plus2place", statusCode: StatusCodes.Status401Unauthorized, title: "priceGrid:NotEnoughSeatAvailable");
                        }
                    }
                }
            }
            else
            {
                string msgErreur = $"ListEvents or Sessions is null or 0, manifid={eventId}, seanceid={sessionId}, identiteid={identityId},profilacheteurid={buyerProfilId}";
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, msgErreur);
                return Problem(detail: msgErreur, statusCode: StatusCodes.Status500InternalServerError, title: "priceGrid:NotEnoughSeatAvailable");
            }


            //Les seats que l'on vient d'ajouter
            if (ThereIsStrapontins && ThereIsPlacesDiscontigues)
            {
                return Ok(new { msg = "discontiguesstrapontins", basket = bask, seatsAdded = allSeats });
            }
            if (ThereIsStrapontins)
            {
                return Ok(new { msg = "strapontins", basket = bask, seatsAdded = allSeats });
            }

            if (ThereIsPlacesDiscontigues)
            {
                return Ok(new { msg = "discontigues", basket = bask, seatsAdded = allSeats });
            }


            return Ok(new { msg = "ok", basket = bask, seatsAdded = allSeats });
        }

        /// <summary>
        /// call api du sponsor, check la validité, si ok, ajoute dans panier_entree, si rien ne fait rien, si ko deflague les places et throw error
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketLineId"></param>
        /// <param name="sponsorSeat"></param>
        /// <param name="cntSponsorUsedCard"></param>
        /// <param name="webUserId"></param>
        /// <param name="apiOffersUrl"></param>
        /// <param name="token"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="SeatId"></param>
        /// <param name="gpId"></param>
        /// <returns></returns>
        [Obsolete("??? ne semble plus utilisé, pas de call")]
        public async Task<bool> AddCardSponsorsAsync(int structureId, int basketLineId, PropertiesSeatSelectionToFlag sponsorSeat, int cntSponsorUsedCard, int webUserId,
            int eventId, int sessionId, int SeatId, int gpId
            )
        //public bool? AddCardSponsorsAsync(int structureId, int basketLineId, PropertiesSeatSelectionToFlag sponsorSeat, ref int cntSponsorUsedCard, int webUserId)
        {
            bool oneCardIsUsed = false;
            if (sponsorSeat?.Sponsor != null && !string.IsNullOrEmpty(sponsorSeat.Sponsor.SponsorCode))
            {
                switch (sponsorSeat.Sponsor.SponsorCode?.ToUpper())
                {
                    case "CARTEAVANTAGE":
                        Logger.Info(structureId, $"sponsor CARTEAVANTAGE panier_entree_id : {basketLineId}");
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"sponsor CARTEAVANTAGE panier_entree_id : {basketLineId}");

                        string myCardEnClair = sponsorSeat.Sponsor.SponsorValue[cntSponsorUsedCard];
                        string myCardTronquee = myCardEnClair;
                        if (myCardEnClair.Length > 4)
                            myCardTronquee = $"****{myCardEnClair.Substring(4)}";

                        Logger.Debug(structureId, $"sponsor CARTEAVANTAGE : {myCardEnClair}");
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"sponsor CARTEAVANTAGE : {myCardTronquee}");


                        StructurePrefsDTO sponsorSettingInStructurePrefsApiUrl = _structurePrefsManager.GetStructurePrefs(structureId).Where(sp => sp.PreferenceCle.Trim().ToUpper() == "SPONSORAPIURL").FirstOrDefault();
                        StructurePrefsDTO sponsorSettingInStructurePrefsApiAuthorization = _structurePrefsManager.GetStructurePrefs(structureId).Where(sp => sp.PreferenceCle.Trim().ToUpper() == "SPONSORAUTHORIZATION").FirstOrDefault();
                        StructurePrefsDTO sponsorSettingInStructurePrefsSponsorName = _structurePrefsManager.GetStructurePrefs(structureId).Where(sp => sp.PreferenceCle.Trim().ToUpper() == "SPONSOR_PARTNER").FirstOrDefault();

                        SponsorHelper sponsorHelper = new SponsorHelperCarteAvantage();

                        var resultSponsorCheckNumber = await sponsorHelper.SponsorCheckNumberAsync(sponsorSettingInStructurePrefsApiUrl.PreferenceValeur, sponsorSettingInStructurePrefsApiAuthorization.PreferenceValeur, myCardEnClair).ConfigureAwait(false);


                        //config

                        //var resultSponsorCheckNumber = await sponsorHelper.SponsorCheckNumberAsync("123", "456", myCardEnClair);
                        if (!(resultSponsorCheckNumber.StatusCode.Equals(HttpStatusCode.OK) || resultSponsorCheckNumber.StatusCode.Equals(HttpStatusCode.Created)))
                        {
                            //déflaggue la place qui vient d'être flaguée
                            //  await ApiOffersHelper.UnFlagSeatsSelection(apiOffersUrl, token, structureId, eventId, sessionId, webUserId, new List<int>() { SeatId });
                            _seatManager.UnFlag(structureId, 0, sessionId, webUserId, new List<int>() { SeatId }, "");
                            _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Déflaggue les places");
                            _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"SponsorCheckNumberAsync - Carte Avantage response = {resultSponsorCheckNumber.StatusCode}");

                            string detail = $"Carte Avantage response = {resultSponsorCheckNumber.StatusCode}";
                            string title = "CardError";
                            Exception ex = new ConstraintException(title + "|" + detail);
                            throw ex;
                            //return Problem(detail: detail, title, statusCode: StatusCodes.Status403Forbidden);
                        }

                        string sponsorReferenceCrypte = resultSponsorCheckNumber.Content;

                        Logger.Debug(structureId, $"sponsor CARTEAVANTAGE : {sponsorReferenceCrypte}");
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"sponsor CARTEAVANTAGE sponsorReferenceCrypte: {sponsorReferenceCrypte}");

                        var remainingCardSponsor = _sponsorManager.GetSponsorUsedRemaining(structureId, sessionId, gpId, sponsorReferenceCrypte);
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"sponsor CARTEAVANTAGE remainingCardSponsor: {remainingCardSponsor}");
                        Logger.Debug(structureId, $"sponsor CARTEAVANTAGE remainingCardSponsor: {remainingCardSponsor}");

                        var sponsorAlreadyExistInAnyBasket = _sponsorManager.GetSponsorReferenceOnAllBaskets(structureId, eventId, sponsorReferenceCrypte);
                        remainingCardSponsor -= sponsorAlreadyExistInAnyBasket.Count();

                        if (remainingCardSponsor <= 0)
                        {
                            //déflaggue la place qui vient d'être flaguée
                            //await ApiOffersHelper.UnFlagSeatsSelection(apiOffersUrl, token, structureId, eventId, sessionId, webUserId, new List<int>() { SeatId });
                            //déflaggue la place qui vient d'être flaguée
                            _seatManager.UnFlag(structureId, 0, sessionId, webUserId, new List<int>() { SeatId }, "");

                            _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Pas assez de crédit sur la carte");
                            _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Déflaggue les places sur CARTEAVANTAGE remainingCardSponsor: {remainingCardSponsor}");

                            string detail = $"Pas assez de crédit sur la carte";
                            string title = "NotEnoughCreditOnTheCard";
                            Exception ex = new ConstraintException(title + "|" + detail);
                            throw ex;
                            //return Problem(detail: detail, title, statusCode: StatusCodes.Status403Forbidden);
                        }

                        _basketManager.AddSponsorSeatLine(structureId, basketLineId, sponsorReferenceCrypte, 1);

                        oneCardIsUsed = true;
                        break;

                    default: break;

                }


            }

            return oneCardIsUsed;
        }

        /// <summary>
        /// Flag une liste de sièges
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="webUserId"></param>
        /// <param name="token"></param>
        /// <param name="langCode"></param>
        /// <param name="seatsId"></param>
        /// <param name="categsId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{structureId}/flagseatsmanuallyajax/{eventId}/{sessionId}/{webUserId}/{categId}/{langCode}")]
        public async Task<IActionResult> FlagsSeatsManuallyAjax(int structureId, int eventId, int sessionId, int categId, int webUserId, string token, string langCode, List<int> seatsId)
        {
            if (seatsId == null)
            {
                Logger.Error(structureId, $"FlagAjax : Le paramètre seatsId est null");

                var error = new ErrorViewModel
                {
                    IpAddress = RodrigueHttpContext.IpAddress,
                    Code = (int)HttpStatusCode.NotAcceptable,
                    Message = "FlagsSeatsManuallyAjax, seatsId is null",
                    Date = DateTime.Now,
                    HtmlSelector = ""
                };
                return View("Error", error);

            
            }

            Logger.Info(structureId, $"FlagAjax ({structureId}, {webUserId}, {seatsId.Count})");

            try
            {
                List<SeatDTO> listOfListSeats = _seatManager.FlagManually(structureId, eventId, sessionId, webUserId, seatsId, "T" + webUserId, 0,0);
                return Ok(listOfListSeats);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"flag {ex.StackTrace} \n {ex.Message}");
                throw;
            }
        }

        [HttpPost]
        [Route("{structureId}/unflagajax/{basketId}/{eventId}/{sessionId}/{webUserId}/{langCode}")]
        public async Task<IActionResult> UnFlagAjax(int structureId, int basketId, int eventId, int sessionId, int webUserId, string token, string langCode, List<int> seatsId)
        {
            if (seatsId == null)
            {
                Logger.Error(structureId, $"UnFlagAjax : Le paramètre seatsId est null");

                var error = new ErrorViewModel
                {
                    IpAddress = RodrigueHttpContext.IpAddress,
                    Code = (int)HttpStatusCode.NotAcceptable,
                    Message = "UnFlagAjax, seatsId is null",
                    Date = DateTime.Now,
                    HtmlSelector = ""
                };
                return View("Error", error);                
            }

            Logger.Info(structureId, $"UnFlagAjax ({structureId}, {basketId}, {webUserId}, {seatsId.Count})");

            try
            {
                if (basketId > 0)
                {
                    //Supprime les places du panier (panier_entree)
                    if (_basketManager.DeleteSeatsOfSessionBasket(structureId, sessionId, basketId, seatsId))
                    {
                        //supprime le produit carte adhésion si il y en a une
                        if (_basketManager.DeleteAllCartesAdhesion(structureId, basketId))
                        {
                            //déflaggue la place qui vient d'être flaguée
                            _seatManager.UnFlag(structureId, 0, sessionId, webUserId, seatsId, "");
                            //await ApiOffersHelper.UnFlagSeatsSelection(apiOffersUrl, token, structureId, eventId, sessionId, webUserId, new List<int>() { seatFlage.SeatId });
                            _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Déflaggue des places {string.Join(",", seatsId)}");
                        }
                    }

                    var newBasket = GetBasket(structureId, basketId, webUserId, langCode);
                    return Ok(newBasket);
                }
                else
                {
                    //déflaggue la place qui vient d'être flaguée
                    _seatManager.UnFlag(structureId, 0, sessionId, webUserId, seatsId, "");
                    return Ok(); // panier à 0 : les places etaient juste flagées
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Unflag {ex.StackTrace} \n {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Obtient le panier en cours 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId">Id du panier</param>
        /// <param name="webUserId">Id du web user </param>
        /// <param name="langCode">Code de la langue (fr, de,...)</param>
        /// <returns></returns>
        private BasketDTO GetBasket(int structureId, int basketId, int webUserId, string langCode)
        {
            Logger.Trace(structureId, $"---- start GetBasket ---- {basketId}, {webUserId}, {langCode}");
            try
            {
                var listBask = _basketManager.GetAllBasketInfo(structureId, basketId, webUserId);
                if (listBask.Count == 1)
                {
                    var newBasket = listBask[0];

                    Logger.Debug(structureId, $"newBasket {newBasket.BasketId}");

                    if (newBasket.ListAllSeatsUnitSales()?.Count == 0 && newBasket.ListAbonnements?.Count == 0 && newBasket.ListProductCartesAdhesion?.Count == 0 &&
                        newBasket.ListSeatsAboMulti?.Count == 0 && newBasket.ListProduitsWT?.Count == 0)
                    {
                        //Si aucune ligne dans panier_entree alros on invalide le panier (etat=I)
                        bool isInvalidPanier = _basketManager.UpdateEtatPanier(structureId, basketId, "I") > 0;
                        Logger.Debug(structureId, $"newBasket isInvalidPanier {isInvalidPanier}");

                        if (isInvalidPanier)
                        {
                            newBasket.Etat = "I";
                        }
                    }

                    return newBasket;
                }
                else
                {
                    return new BasketDTO() // pas de panier, retourne panier vide, id = 0
                    {
                        BasketId = 0
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"GetBasket : {ex.Message} \n {ex.StackTrace}");
                throw;
            }
        }
    }
}
