{"Files": [{"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Basket\\style.less", "PackagePath": "staticwebassets\\css\\Basket\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\CrossSelling\\style.less", "PackagePath": "staticwebassets\\css\\CrossSelling\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\HomeModular\\style.less", "PackagePath": "staticwebassets\\css\\HomeModular\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Insurance\\style.less", "PackagePath": "staticwebassets\\css\\Insurance\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\ProductDetails\\style.less", "PackagePath": "staticwebassets\\css\\Product\\ProductDetails\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\style.less", "PackagePath": "staticwebassets\\css\\Product\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Session\\style.less", "PackagePath": "staticwebassets\\css\\Session\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\catalog\\style.less", "PackagePath": "staticwebassets\\css\\catalog\\style.less"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\brands.min.css", "PackagePath": "staticwebassets\\css\\fontawesome\\brands.min.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\fontawesome.min.css", "PackagePath": "staticwebassets\\css\\fontawesome\\fontawesome.min.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\solid.min.css", "PackagePath": "staticwebassets\\css\\fontawesome\\solid.min.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.ttf", "PackagePath": "staticwebassets\\css\\webfonts\\fa-brands-400.ttf"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.woff2", "PackagePath": "staticwebassets\\css\\webfonts\\fa-brands-400.woff2"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.ttf", "PackagePath": "staticwebassets\\css\\webfonts\\fa-regular-400.ttf"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.woff2", "PackagePath": "staticwebassets\\css\\webfonts\\fa-regular-400.woff2"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.ttf", "PackagePath": "staticwebassets\\css\\webfonts\\fa-solid-900.ttf"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.woff2", "PackagePath": "staticwebassets\\css\\webfonts\\fa-solid-900.woff2"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.ttf", "PackagePath": "staticwebassets\\css\\webfonts\\fa-v4compatibility.ttf"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.woff2", "PackagePath": "staticwebassets\\css\\webfonts\\fa-v4compatibility.woff2"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\angle_button_white.svg", "PackagePath": "staticwebassets\\img\\angle_button_white.svg"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\sessionnotavailable.png", "PackagePath": "staticwebassets\\img\\sessionnotavailable.png"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\ticket-shape-bottom.svg", "PackagePath": "staticwebassets\\img\\ticket-shape-bottom.svg"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\basket\\basket.js", "PackagePath": "staticwebassets\\js\\basket\\basket.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\bootstrap-spinner\\bootstrap-input-spinner.js", "PackagePath": "staticwebassets\\js\\bootstrap-spinner\\bootstrap-input-spinner.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\catalog\\catalog.js", "PackagePath": "staticwebassets\\js\\catalog\\catalog.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\categprices\\categprices.js", "PackagePath": "staticwebassets\\js\\categprices\\categprices.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\commons.js", "PackagePath": "staticwebassets\\js\\commons.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\crossSelling\\crossSelling.js", "PackagePath": "staticwebassets\\js\\crossSelling\\crossSelling.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\feedbook\\feedbookForm.js", "PackagePath": "staticwebassets\\js\\feedbook\\feedbookForm.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\homemodular\\homemodular.js", "PackagePath": "staticwebassets\\js\\homemodular\\homemodular.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\insurance\\insurance.js", "PackagePath": "staticwebassets\\js\\insurance\\insurance.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\lessModify.js", "PackagePath": "staticwebassets\\js\\lessModify.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\product.js", "PackagePath": "staticwebassets\\js\\product\\product.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\productDetails\\productDetailsCADH.js", "PackagePath": "staticwebassets\\js\\product\\productDetails\\productDetailsCADH.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\seatsSelection\\seatsSelection.js", "PackagePath": "staticwebassets\\js\\seatsSelection\\seatsSelection.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\pano.js", "PackagePath": "staticwebassets\\js\\session\\pano.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\seatplan.js", "PackagePath": "staticwebassets\\js\\session\\seatplan.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\session.js", "PackagePath": "staticwebassets\\js\\session\\session.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\tunnel\\tunnel.js", "PackagePath": "staticwebassets\\js\\tunnel\\tunnel.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\widget.js", "PackagePath": "staticwebassets\\js\\widget.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterne.html", "PackagePath": "staticwebassets\\websiteexterneDemo\\WebSiteExterne.html"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterneCallLocal.html", "PackagePath": "staticwebassets\\websiteexterneDemo\\WebSiteExterneCallLocal.html"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\testingCallOffersLocal.html", "PackagePath": "staticwebassets\\websiteexterneDemo\\testingCallOffersLocal.html"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteExterneTunnel.html", "PackagePath": "staticwebassets\\websiteexterneDemo\\websiteExterneTunnel.html"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.css", "PackagePath": "staticwebassets\\websiteexterneDemo\\websiteexterne.css"}, {"Id": "D:\\WORK\\Themis_core_DEV_publish\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.js", "PackagePath": "staticwebassets\\websiteexterneDemo\\websiteexterne.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Core.Themis.Widgets.Offers.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Core.Themis.Widgets.Offers.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.Core.Themis.Widgets.Offers.props", "PackagePath": "build\\Core.Themis.Widgets.Offers.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.Core.Themis.Widgets.Offers.props", "PackagePath": "buildMultiTargeting\\Core.Themis.Widgets.Offers.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.Core.Themis.Widgets.Offers.props", "PackagePath": "buildTransitive\\Core.Themis.Widgets.Offers.props"}], "ElementsToRemove": []}