﻿--DECLAR<PERSON> @pIdentiteId INT = 721
--DECLAR<PERSON> @pSeanceId INT = 43 --2385

DECLARE @dossierId INT, @manifId INT
DECLARE @SqlForDossier VARCHAR(4000), @SqlForEntree VARCHAR(4000)

/* Création et remplissage de la table #Dossier*/
CREATE TABLE #Dossier (
	manifestation_id INT,
	dossier_id INT,
	identite_id INT, 
	commande_id INT,
	abon_manif_id INT,
	seance_id INT, 
	dossier_v INT,
	operateur_id INT,
	dossier_montant DECIMAL(18,10),
	dossier_montant1 DECIMAL(18,10),
	dossier_montant2 DECIMAL(18,10),
	dossier_montant3 DECIMAL(18,10),
	dossier_montant4 DECIMAL(18,10),
	dossier_montant5 DECIMAL(18,10),
	dossier_montant6 DECIMAL(18,10),
	dossier_montant7 DECIMAL(18,10),
	dossier_montant8 DECIMAL(18,10),
	dossier_montant9 DECIMAL(18,10),
	dossier_montant10 DECIMAL(18,10),
	dossier_c CHAR(50),
	dossier_etat CHAR(1),
	dossier_icone INT,
	dossier_numero INT,
	dossier_nbplace INT,
	dossier_montantpayer DECIMAL(18,10),
	dossier_facture INT,
	dossier_client_nom CHAR(40),
	num_paiement INT,
	date_operation DATETIME,
	operation_id INT,
	filiere_id INT
)

/*Création et remplissage de la table #Entree*/
CREATE TABLE #Entree (
	manifestation_id INT,
	entree_id INT,
	seance_id INT, 
	dossier_id INT,
	entree_etat CHAR(1),
	categorie_id INT, 
	type_tarif_id INT,
	valeur_tarif_stock_id INT,
	ValeurTarifStockVersion INT,
	numero_billet DECIMAL(19,0),
	reference_unique_physique_id INT,
	reference_unique_logique_id INT,
	lieu_configuration_id INT,
	alotissement_id INT,
	reserve_id INT,
	contingent_id INT,
	derniere_modif BIGINT,
	code_anu CHAR(20) NULL,
	iindex INT,
	flag_section CHAR(10) NULL,
	montant1 DECIMAL(18,10) NULL,
	montant2 DECIMAL(18,10) NULL,
	montant3 DECIMAL(18,10) NULL,
	montant4 DECIMAL(18,10) NULL,
	montant5 DECIMAL(18,10) NULL,
	montant6 DECIMAL(18,10) NULL,
	montant7 DECIMAL(18,10) NULL,
	montant8 DECIMAL(18,10) NULL,
	montant9 DECIMAL(18,10) NULL,
	montant10 DECIMAL(18,10) NULL,
	dateoperation DATETIME,
	controleacces VARCHAR(50) NULL,
)

/* Définission des infos de commandes pour le fetch*/
DECLARE CommandeLigneInfos CURSOR SCROLL FOR 

	SELECT cl.dossier_id, cl.manifestation_id FROM commande_ligne cl
	INNER JOIN commande c ON cl.commande_id = c.commande_id
	WHERE (c.identite_id = @pIdentiteId or cl.identite_id = @pIdentiteId)
	AND cl.seance_id = @pSeanceId

OPEN CommandeLigneInfos; 
FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId;

/* Remplissage des tables temporaire*/
WHILE @@FETCH_STATUS=0 
BEGIN 
	SET @SqlForDossier = 
	'INSERT INTO #Dossier 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_dossier, d.*
		FROM dossier_' + LTRIM(STR(@manifId)) + ' d
		WHERE d.dossier_id = ' + LTRIM(STR(@dossierId))

	SET @SqlForEntree = 
	'INSERT INTO #Entree 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_entree, e.*
		FROM entree_' + LTRIM(STR(@manifId)) + ' e
		WHERE e.dossier_id = ' + LTRIM(STR(@dossierId))

	PRINT(@SqlForDossier)
	PRINT(@SqlForEntree)

	EXEC(@SqlForDossier)	
	EXEC(@SqlForEntree)

FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId; 

END

CLOSE CommandeLigneInfos; 
DEALLOCATE CommandeLigneInfos;

SELECT * 
FROM #Entree e
INNER JOIN #Dossier d ON e.dossier_id = d.dossier_id AND e.manifestation_id = d.manifestation_id
WHERE d.dossier_etat = 'B'

DROP TABLE #Dossier
DROP TABLE #Entree