/*
DECLARE @pBarcodes VARCHAR(MAX) = '8030004530'
*/

SELECT c.commande_id FROM recette r
INNER JOIN commande_ligne cl ON r.dossier_id = cl.dossier_id 
	AND r.seance_id = cl.seance_id
	AND r.manifestation_id = cl.manifestation_id
INNER JOIN commande c ON cl.commande_id = c.commande_id
WHERE (r.externe IN (SELECT name FROM splitstring(@pBarcodes,',')) 
	OR r.motif IN (SELECT name FROM splitstring(@pBarcodes,','))) 