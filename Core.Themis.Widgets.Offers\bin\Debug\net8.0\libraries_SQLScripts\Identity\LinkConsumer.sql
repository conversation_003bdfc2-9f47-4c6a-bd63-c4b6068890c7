/*
declare @pIdentityIdConnected int
declare @pIdentityId int


set @pIdentityIdConnected =  749
set @pIdentityId =  490
*/

	INSERT INTO groupe_consomateur(donneur_ordre_identite_id,consomateur_identite_id)
				select @pIdentityIdConnected, @pIdentityId 
				where 0=(select  COUNT(*)  from groupe_consomateur where donneur_ordre_identite_id=@pIdentityIdConnected and consomateur_identite_id=@pIdentityId )
			
	select @pIdentityId	


