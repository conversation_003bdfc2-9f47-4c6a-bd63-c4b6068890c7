﻿/*
DECLARE @pCategsId varchar(max)= '10002, 10004, 10005,10007'
DECLARE @pSessionsId VARCHAR(MAX) = '22,2369'
DECLARE @pLangCode VARCHAR(2) = 'FR'
*/

DECLARE @langueId int = (SELECT langue_id FROM langue where langue_code = @pLangCode)



SELECT DISTINCT vts.type_tarif_id, ISNULL(ttt.type_tarif_nom, tt.type_tarif_nom) as type_tarif_nom 
FROM valeur_tarif_stock[eventId] vts
INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
INNER JOIN filieres_droits fd ON fd.type_tarif_id = tt.type_tarif_id
INNER JOIN filiere f ON f.filiere_id = fd.filiere_id
LEFT OUTER JOIN traduction_type_tarif ttt ON ttt.type_tarif_id = tt.type_tarif_id
 WHERE tt.type_tarif_groupe IN (11,12,13,14, 5, 15 ) -- and vts.vts_grille1<>-1
AND 
vts_v =(	SELECT MAX(vts_v) FROM valeur_tarif_stock[eventId] vts2
					WHERE vts2.tarif_logique_id=vts.tarif_logique_id
					AND vts2.seance_id=vts.seance_id
					AND vts2.categ_id= vts.categ_id
					AND vts2.type_tarif_id= vts.type_tarif_id
		) --and vts.vts_grille1 >= 0
		and seance_id IN (SELECT name FROM splitstring(@pSessionsId, ',')) AND categ_id IN (SELECT name FROM splitstring(@pCategsId,','))
		 ORDER BY vts.type_tarif_id, ISNULL(ttt.type_tarif_nom, tt.type_tarif_nom)

