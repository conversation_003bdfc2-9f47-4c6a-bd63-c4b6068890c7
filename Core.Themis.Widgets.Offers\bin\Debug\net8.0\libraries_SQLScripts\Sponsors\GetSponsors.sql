/*
  CREATE TABLE #tmpSponsors 
        ( 
        
           sponsor_id int, 
           sponsor_code VARCHAR(MAX),  
           type_tarif_id int
        ) 



insert into #tmpSponsors values (1,'CARTEAVANTAGE', 5650020)
insert into #tmpSponsors values (1, 'CARTEAVANTAGE', 5650034)
insert into #tmpSponsors values (2,'CARTEAUTRE', 5650021)



select sponsor_id, sponsor_code, tt.type_tarif_id,  ts.sponsor_id as Id, tt.type_tarif_id, type_tarif_nom , type_tarif_code from #tmpSponsors ts
inner join type_tarif tt on tt.type_tarif_id = ts.type_tarif_id

drop table #tmpSponsors

declare @mpId int 
select @mpId = convert(int,  preference_valeur) from structure_prefs where preference_cle ='SPONSOR_MODEPAIEMENT'
declare @mpName varchar(100) 
select @mpName =  preference_valeur from structure_prefs where preference_cle ='SPONSOR_PARTNER'

select replace(preference_cle, 'SPONSOR_MAX_', '') as preference_cle, preference_id, preference_valeur
into #structure_prefs_tarifs 
from structure_prefs
where preference_cle like 'SPONSOR_MAX_%'


select 1 as sponsor_id, @mpName as sponsor_code, convert(int,  preference_cle) as type_tarif_id, tt.type_tarif_nom, 1 as Id, tt.mode_paie_id, tt.ValeurTarif 
from #structure_prefs_tarifs
inner join type_tarif tt on tt.type_tarif_id = convert(int,  preference_cle)
inner join mode_paiement mp on mp.mode_paie_id = tt.mode_paie_id
where convert(int,preference_valeur) > 0 and tt.mode_paie_id = @mpId

drop table #structure_prefs_tarifs
*/
declare @mpId int 
select @mpId = convert(int,  preference_valeur) from structure_prefs where preference_cle ='SPONSOR_MODEPAIEMENT' and preference_valeur not like '%xx%'
declare @mpCode varchar(100) 
select @mpCode =  preference_valeur from structure_prefs where preference_cle ='SPONSOR_PARTNER'

declare @mpName varchar(100) 
select @mpName =  preference_valeur from structure_prefs where preference_cle ='SPONSOR_PARTNER_NAME'


select replace(preference_cle, 'SPONSOR_MAX_', '') as preference_cle, preference_id, preference_valeur
into #structure_prefs_tarifs 
from structure_prefs
where preference_cle like 'SPONSOR_MAX_%'


select 1 as sponsor_id, @mpCode as sponsor_code, @mpName as sponsor_name, 1 as Id, tt.type_tarif_nom,  convert(int,  preference_cle) as type_tarif_id, tt.type_tarif_code, tt.mode_paie_id, tt.ValeurTarif * 100 as ValeurTarif
from #structure_prefs_tarifs
inner join type_tarif tt on tt.type_tarif_id = convert(int,  preference_cle)
inner join mode_paiement mp on mp.mode_paie_id = tt.mode_paie_id
where convert(int,preference_valeur) > 0 and tt.mode_paie_id = @mpId

drop table #structure_prefs_tarifs



