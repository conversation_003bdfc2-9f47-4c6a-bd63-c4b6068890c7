
-- Sélectionne les manifestations de getdate() - @pFromMinutes (ou 0) à getdate() + @pToMinutes (ou 20 ans)

/* declare @pDuration int = -1 */

if (@pToMinutes = -1)
	set @pToMinutes = 60*24*365*20 /* 20 ans */
if (@pFromMinutes = -1)
	set @pFromMinutes = 0 

SELECT Distinct m.manifestation_id as eventId, manifestation_nom as eventName, seance_id as sessionId, seance_date_deb as sessionStartDate, 
l.lieu_nom as placeName,l.lieu_id as placeId, lc.lieu_config_id,lc.lieu_config_nom 
FROM seance s 
INNER JOIN manifestation m ON m.manifestation_id = s.manifestation_id
INNER JOIN lieu l ON l.lieu_id=s.lieu_id
INNER JOIN lieu_configuration lc ON lc.lieu_config_id=s.lieu_config_id
WHERE
--s.seance_date_deb > getdate() AND 
s.seance_date_deb > dateadd(MINUTE, -@pFromMinutes,GETDATE()) and
s.seance_date_deb < DATEADD(MINUTE, @pToMinutes, GETDATE()) AND s.supprimer<>'O' and options='N' 

