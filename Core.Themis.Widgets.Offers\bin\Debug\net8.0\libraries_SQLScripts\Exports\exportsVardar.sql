﻿---- exportVardar 1.3 avec Produits
CREATE table #TmpEntreeVardar2  (
	code_base varchar(250) 
	,identifiant varchar(250)
	,commande_numero varchar(250) 
	,manif_nom varchar(250)
	,lieu_nom varchar(250)
	,date_seance datetime
	,place_lib_categ varchar(250)
	,Place_lib_denom varchar(250)
	,place_montant_euros varchar(250)
	,place_montant_dg_eu varchar(250)
	--,Date_paiement varchar(250)
	--,Heure_paiement varchar(250)
	,date_paiement datetime null
	,client_nom varchar(250)
	,dossier_client_nom varchar(250)
	,commentaire varchar(250)
	,billet_etat varchar(250)
	--,Date_operation date
	--,Heure_operation time
	,entree_id int
	,rang varchar(10), siege varchar(10), zone_name varchar(50), floor_name varchar(50), section_name varchar(50)	
	,seance_id int
	,date_operation datetime	
	,place_lib_tarif varchar(50)
	,mode_paiement varchar(50)
	,barcode varchar(50)
 )

DECLARE @C VARCHAR(8000)

DECLARE @manif_id int 
DECLARE @seance_id int 

DECLARE @DateDebstr as varchar(50)
DECLARE @DateFinstr as varchar(50)
DECLARE @DateSortiestr as varchar(8)

DECLARE @DateDeb as datetime
DECLARE @DateFin as datetime 

declare @sinceMinutes int = convert(int, @psinceminutes)


IF (@sinceMinutes <>0)
BEGIN
	SET @DateDeb = (select (dateadd(n , - @sinceMinutes, getdate() )))
	SET @DateFin = getdate()
END
ELSE
BEGIN
	SET @DateDeb = @pdateFrom
	SET @DateFin = @pdateTo
END

SET @DateDebstr =convert(VARCHAR, @DateDeb, 103) + ' ' + convert(VARCHAR, @DateDeb, 108)
SET @DateFinstr = convert(VARCHAR, @datefin, 103) + ' ' + convert(VARCHAR, @datefin, 108)
SET @DateSortiestr =convert(varchar ,CONVERT(datetime,GETDATE()-1,103),112)

DECLARE @ManifID int 
DECLARE @SeanceID int 
DECLARE @SQL VARCHAR(8000)


SELECT DISTINCT manif_id, seance_id 
into #manifSeanceToCheck from histodossier 
where date_operation >= @DateDeb and manif_id>0

if (@pplaceid > 0)
begin
	delete #manifSeanceToCheck where seance_id not in (select seance_id from seance where lieu_id= @pplaceId)	
end
if (@peventid > 0)
begin
	delete #manifSeanceToCheck where manif_id <> @peventid
end

DELETE #manifSeanceToCheck WHERE seance_id not in (select seance_id from seance where seance_date_deb >= @pStartDate and seance_date_deb <= @pEndDate)



--ouverture du curseur
DECLARE Curseur  CURSOR FOR
SELECT DISTINCT manif_id, seance_id from #manifSeanceToCheck

OPEN curseur
FETCH curseur INTO @ManifID, @SeanceID
--génération de la requête boucle
WHILE @@fetch_status = 0 
	BEGIN 
		SET @SQL = 'SELECT DISTINCT
			st.structure_nom
			,convert(varchar,d.identite_id) +''_''+ convert(varchar,d.dossier_id) + ''_'' + convert(varchar,e.entree_id)  as ''Reference unique''
			,d.commande_id as ''Numéro de commande''
			,m.manifestation_nom as ''Nom de la manifestation'' 
			,lc.lieu_config_nom ''Lieu Nom ''
			
			,seance_date_deb
			,cat.categ_nom as ''Nom de la catégorie''
			,dnm.denom_nom as ''Denomination''
			,e.montant1+e.montant2 ''Montant Place''
			,e.montant2 ''Frais Place''

			,(SELECT dateoperation from entreesvg_' + convert(varchar,@ManifID) + '
				 where entree_etat = ''P'' and dossier_id = e.dossier_id and entree_id = e.entree_id and dossier_v = e.dossier_v ) as ''Date Paiement''
			
			,rtrim(ltrim(i.identite_nomprenom))''Nom du Client''
			,rtrim(ltrim(d.dossier_client_nom))
			,rtrim(ltrim(ci.commentaire))		
			,case e.entree_etat when  ''R'' then ''Réservé'' when ''P'' then ''Payé'' when ''B'' then ''Edité'' when ''L'' then ''Libéré'' else '''' end as ''Etat de la place''
	
			,e.entree_id
			,rlp.rang, rlp.siege, z.zone_nom, et.etage_nom, sc.section_nom			
			,e.seance_id
			,e.dateoperation 
			,tt.type_tarif_nom			
				,(SELECT top 1 isnull(mp.mode_paie_nom,''ACOMPTE'') from compte_client cc LEFT OUTER JOIN mode_paiement mp on mp.mode_paie_id = cc.mode_paiement_id  
					WHERE cc.cc_numpaiement = d.num_paiement and cc.mode_paiement_id >0  and d.num_paiement > 0  and d.dossier_montant>0) as ''Mode de paiement'' 			
		,rtrim(ltrim(r.motif))
			
		FROM ENTREEsvg_' + convert(varchar,@ManifID) + ' e 
		INNER JOIN entree_' + convert(varchar,@ManifID) + ' e1 on e1.entree_id = e.entree_id
		INNER JOIN dossier_' + convert(varchar,@ManifID) + ' d on d.dossier_id=e.dossier_id
		INNER JOIN categorie cat on cat.categ_id = e.categorie_id
		INNER JOIN type_tarif tt on tt.type_tarif_id = e.type_tarif_id

		INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id  = e1.reference_unique_physique_id 
		INNER JOIN lieu_physique lp on lp.lieu_physique_id = rlp.lieu_physique_id
		INNER JOIN lieu_configuration lc on lc.lieu_physique_id = lp.lieu_physique_id

		INNER JOIN section sc on sc.section_id = rlp.section_id

		INNER JOIN zone z on z.zone_id = rlp.zone_id 
		INNER JOIN etage et on et.etage_id = rlp.etage_id 
		INNER JOIN seance s on s.seance_id=d.seance_id
		INNER JOIN commande_ligne cl on cl.dossier_id = e.dossier_id and cl.seance_id = e.seance_id and cl.type_ligne = ''DOS''
		LEFT JOIN commande_infos ci ON ci.commande_id = cl.commande_id and ci.commande_id = cl.commande_id
		INNER JOIN Commande_Ligne_comp clc ON clc.commande_ligne_id = cl.commande_ligne_id 
		INNER JOIN filiere f on f.filiere_id = clc.filiere_id
		INNER JOIN manifestation m on m.manifestation_id = cl.manifestation_id
		INNER JOIN identite i on i.identite_id = d.identite_id
		LEFT OUTER JOIN global_appellation ga on ga.appellation_id = i.appellation_id
		INNER JOIN type_tarif_groupe ttg on ttg.type_tarif_groupe_id = tt.type_tarif_groupe
		LEFT OUTER JOIN formule_abonnement fa on fa.form_abon_id = cl.formule_id
		LEFT OUTER JOIN recette r on r.entree_id = e.entree_id and r.seance_id = e.seance_id and r.dossier_id = e.dossier_id
				
		INNER JOIN manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		LEFT OUTER JOIN super_groupe sg on sg.id_super_groupe = mg.super_groupe_id
		INNER JOIN [structure] st on st.structure_id =st.structure_id
		LEFT OUTER JOIN manifestation_genre gen on gen.id = m.ID_genre  --- Attention ceci est le sous-genre
		LEFT OUTER JOIN manifestation_groupe_genre mgg on mgg.id = gen.groupe_id --- Attention ceci est le genre
		LEFT OUTER JOIN Seance_Cible scbl on scbl.Seance_id = s.seance_Id
		LEFT OUTER JOIN cible cbl on cbl.id = scbl.Cible_id

		LEFT OUTER JOIN filiere f2 on f2.filiere_id = i.filiere_id
		LEFT OUTER JOIN lieu l on l.lieu_id = s.lieu_id 
		LEFT OUTER JOIN denomination dnm on dnm.denom_id = rlp.denomination_id

		WHERE e.entree_etat in (''B'',''P'',''R'',''L'') and e.dateoperation BETWEEN '''+ @DateDebstr +''' and '''+ @DateFinstr +'''
		ORDER BY e.dateoperation'

		print @sql
	
		INSERT INTO #TmpEntreeVardar2 EXEC (@SQL)

	FETCH curseur INTO @ManifID, @SeanceID
	END
CLOSE curseur
DEALLOCATE curseur  

SELECT code_base,identifiant, commande_numero, manif_nom, lieu_nom, date_seance, place_lib_categ,place_lib_denom, rang, siege, 
			zone_name, floor_name, section_name,		
			place_lib_tarif,
			place_montant_euros, place_montant_dg_eu,
			date_paiement,
			client_nom, dossier_client_nom, commentaire, billet_etat, mode_paiement, barcode, date_operation
			 from #TmpEntreeVardar2 order by date_operation
			--print @bcp


select distinct commande_numero 
into #cmdnumeros from #tmpEntreeVardar2 


DROP TABLE #TmpEntreeVardar2

SELECT @DateDeb as dateFrom, @dateFin as dateTo


CREATE table #TmpEntreeVardarProduits  (
	code_base varchar(250) 
	,identifiant varchar(250)
	,commande_numero varchar(250) 
	,manif_nom varchar(250)
	,lieu_nom varchar(250)
	,date_seance datetime
	,produit_nom varchar(100)
	,produit_id int
	,nombre int
	,produit_etat varchar(10)
	,client_nom varchar(250)
	,dossier_client_nom varchar(250)
	,commentaire varchar(250)
	--,billet_etat varchar(250)
	--,Date_operation date
	--,Heure_operation time
	
	,seance_id int
	,date_operation datetime	
	,mode_paiement varchar(50)
 )

INSERT INTO #TmpEntreeVardarProduits
SELECT 
		st.structure_nom
			,convert(varchar,dp.identite_id) +'_'+ convert(varchar,dp.dos_prod_id) + '_' + convert(varchar,dp.produit_id)  as 'Reference unique'
			,dp.commande_id as 'Numéro de commande'
			,m.manifestation_nom as 'Nom de la manifestation' 
			,lc.lieu_config_nom as 'Lieu Nom'
			,seance_date_deb
				
			,p.produit_nom
			,p.produit_id
			,dp.nb_produit
			,case dp.dos_prod_etat when  'R' then 'Réservé' when 'P' then 'Payé' when 'B' then 'Edité' when 'L' then 'Libéré' else '' end as 'Etat du produit'

			,rtrim(ltrim(i.identite_nomprenom)) 'Nom du Client'
			,rtrim(ltrim(dp.dossier_client_nom))
			,rtrim(ltrim(ci.commentaire))
			,s.seance_id
			,dp.date_operation, 
			(SELECT top 1 isnull(mp.mode_paie_nom,'ACOMPTE') from compte_client cc LEFT OUTER JOIN mode_paiement mp on mp.mode_paie_id = cc.mode_paiement_id  
				WHERE cc.cc_numpaiement = dp.num_paiement and cc.mode_paiement_id >0  and dp.num_paiement > 0  and dp.dos_prod_montant>0) as 'Mode de paiement' 			

FROM commande_ligne cl 
INNER JOIN #cmdnumeros cmdsnum on cmdsnum.commande_numero = cl.commande_id
INNER JOIN [structure] st on st.structure_id =st.structure_id
inner join dossier_produit dp on dp.dos_prod_id = cl.dossier_id
INNER JOIN identite i on i.identite_id = dp.identite_id
INNER JOIN produit p on p.produit_id = dp.produit_id
LEFT JOIN commande_infos ci ON ci.commande_id = cl.commande_id and ci.commande_id = cl.commande_id
LEFT JOIN seance s on s.seance_id = dp.seance_id
LEFT JOIN lieu_configuration lc on lc.lieu_config_id = s.lieu_config_id
LEFT JOIN manifestation m on m.manifestation_id = s.manifestation_id
INNER JOIN Commande_Ligne_comp clc ON clc.commande_ligne_id = cl.commande_ligne_id 

WHERE cl.type_ligne ='PRO' 

SELECT * from #TmpEntreeVardarProduits

DROP TABLE #cmdnumeros
DROP TABLE #TmpEntreeVardarProduits


