﻿--DECLARE @pFormulaId INT = 4
--DECLARE @plangCode varchar(2) = 'DE'

DECLARE @langId INT = ISNULL((SELECT langue_id FROM langue WHERE langue_code = @plangCode), 1)

select distinct m.manifestation_id, m.manifestation_nom
from abonnement_manifestation  am
INNER JOIN formule_abonnement fa ON fa.form_abon_id = am.formule_id
INNER JOIN manifestation m ON am.manif_id = m.manifestation_id
LEFT OUTER JOIN traduction_manifestation tm ON tm.manifestation_id = m.manifestation_id AND tm.langue_id = @langId
WHERE formule_id = @pFormulaId AND m.supprimer = 'N' AND fa.masquer = 'N' 



