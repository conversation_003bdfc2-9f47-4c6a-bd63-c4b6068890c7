﻿/*
  declare @pFieldCodeId int = 4150
*/

  select tfc.id as field_code_id, fieldSpecificCode, tfc.description as field_code_description, area_id, tfgt.id as global_field_code_id, fieldCode, 
  isnull(txt_fr,'') as txt_fr, isnull(txt_en, '') as txt_en, isnull(txt_de,'') as txt_de, isnull(txt_nl, '') as txt_nl, isnull(txt_it, '') as txt_it, isnull(txt_es, '') as txt_es, isnull(txt_pt,'') as txt_pt,
  tv.Id as variable_id, tv.Name as variable_name, tv.Description as variable_description, tv.IsAutoCloseTag as is_auto_close
  from translate_fieldsCodesList tfc
  inner join translate_fieldsGlobalTranslation tfgt on tfgt.id = tfc.global_field_id
  left outer join translate_fieldsVariables tfv on tfv.fieldId = tfgt.id 
  left outer join translate_variables tv on tv.Id = tfv.variableId
  where tfc.id = @pFieldCodeId

  --where tfgt.id = @pGlobalFieldCode
