﻿using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Products.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Widgets;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
//using static PdfSharp.Snippets.Font.NewFontResolver;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class ProductController : Controller
    {
        private static readonly RodrigueNLogger Logger = new();

        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IBasketManager _basketManager;
        private readonly IPartnerManager _partnerManager;
        private readonly ITranslateManager _translateManager;
        private readonly IProductManager _productManager;
        private readonly IGestionTraceManager _gestionTraceManager;
        private readonly IBuyerProfilManager _buyerProfilManager;

        public ProductController(
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            IBasketManager basketManager,
            IPartnerManager partnerManger,
            ITranslateManager translateManager,
            IProductManager productManager,
            IGestionTraceManager gestionTraceManager,
            IBuyerProfilManager buyerProfilManager)
        {
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
            _basketManager = basketManager;
            _partnerManager = partnerManger;
            _translateManager = translateManager;
            _productManager = productManager;
            _gestionTraceManager = gestionTraceManager;
            _buyerProfilManager = buyerProfilManager;
        }

        /// <summary>
        /// ajout de n produits au panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="listProducts"></param>
        /// <returns></returns>
        [HttpPost]
        /// route pour ajout des produits directement, depuis la page produit
        [Route("{structureId}/Product/AddBasketAjax/{identityId}/{webUserId}/{buyerProfilId}/{langCode}")]

        public async Task<IActionResult> ProductAddBasketAjax(int structureId, string langCode,
            int identityId, int webUserId, int buyerProfilId, string token, PropertiesProductsToAddBasket[] listProducts)
        {
            Logger.Info(structureId, $"ProductAddBasketAjax ({structureId}, {langCode}, {identityId}, {webUserId}, {buyerProfilId}, {listProducts.Length})");

            try
            {
                BasketDTO bask = new BasketDTO();
                if (listProducts.Length > 0)
                {
                    bask = _basketManager.CreateBasketIfNotExists(structureId, webUserId, identityId, "C", null);
                    bask.Hash = bask.GetHash();

                    var bout = _productManager.LoadProductsOfBoutique(structureId, langCode, "", 0, 0, 0, 0, 0, 0);

                    foreach (var linkProdConsumer in listProducts)
                    {
                        ProductDTO prod = new();
                        int thisProdId = linkProdConsumer.ProductId;
                        int famId = 0;
                        int sousfamId = 0;
                        foreach (var f in bout.ProductFamiliesList)
                        {
                            foreach (var sf in f.SubFamiliesList)
                            {
                                var thisProdFound = sf.ProductsList.Where(p => p.ProductId == thisProdId).FirstOrDefault();
                                if (thisProdFound != null)
                                {
                                    famId = f.Id;
                                    sousfamId = sf.Id;
                                    prod = thisProdFound;
                                }
                            }
                        }

                        prod.ProductId = thisProdId;
                        prod.ConsommateurConsumerId = linkProdConsumer.ConsumerId;
                        prod.Count = linkProdConsumer.Count;

                        if (prod.GroupName == "CADH")
                        {
                            ProductCarteAdhesion myprod = bout.ProductFamiliesList.Where(f => f.Id == famId).FirstOrDefault()
                                .SubFamiliesList.Where(sf => sf.Id == sousfamId).FirstOrDefault()
                                .ProductsCarteAdhesionList.Where(pr => pr.ProductId == thisProdId).FirstOrDefault();

                            myprod.ConsommateurIdentiteId = linkProdConsumer.ConsumerId;
                            myprod.Count = linkProdConsumer.Count;

                            int r = _basketManager.InsertPanierProduit(structureId, bask.BasketId, myprod);
                        }
                    }
                }
                else
                {
                    _gestionTraceManager.WriteLogErrorMessage(structureId, webUserId, $"received listLinksProdsConsumers.Count == 0 , buyer profil {buyerProfilId}, identityId {identityId}");
                    Logger.Info(structureId, $"received listLinksProdsConsumers.Count == 0 , buyer profil {buyerProfilId}, identityId {identityId}");
                    return Problem("received listLinksProdsConsumers.Count == 0 => grmbl !");
                }

                return Ok(bask);

            }
            catch (Exception ex)
            {
                return Problem(ex.Message);
            }
        }

        /// route pour ajout des produits depuis le panier
        [Route("{structureId}/Product/AddBasketAjax/basket/{identityId}/{webUserId}/{buyerProfilId}/{langCode}")]
        public async Task<IActionResult> ProductAddBasketFromBasketAjax(int structureId, string langCode,
            int identityId, int webUserId, int buyerProfilId, string token, PropertiesProductsToAddBasket[] listProducts)
        {
            Logger.Info(structureId, $"ProductAddBasketFromBasketAjax ({structureId}, {langCode}, {identityId}, {webUserId}, {buyerProfilId}, {listProducts.Length})");

            try
            {
                BasketDTO bask = new();
                if (listProducts.Length > 0)
                {
                    bask = _basketManager.CreateBasketIfNotExists(structureId, webUserId, identityId, "C", null);
                    bask.Hash = bask.GetHash();

                    List<ProductDTO> listProductsRec = _productManager.LoadProducts(structureId, langCode);
                    List<int> listMyProdChoisis = listProducts.Select(p => p.ProductId).ToList();

                    // on commence par supp tous les produits du panier
                    _basketManager.DeleteProductId(structureId, bask.BasketId, listMyProdChoisis);

                    foreach (var productChoisi in listProducts.Where(p => p.Count > 0)) // les prods where count > 0
                    {
                        ProductDTO prod = new();

                        int thisProdId = productChoisi.ProductId;
                        var thisProdFound = new ProductDTO();

                        if (productChoisi.SessionId != 0) // à la séance
                        {
                            thisProdFound = listProductsRec.Where(p => p.ProductId == thisProdId && p.SessionId == productChoisi.SessionId).FirstOrDefault();
                        }
                        else if (productChoisi.EventId != 0) // à la manif
                        {
                            thisProdFound = listProductsRec.Where(p => p.ProductId == thisProdId && p.EventId == productChoisi.EventId).FirstOrDefault();
                        }
                        else // ni manif, ni séance
                        {
                            thisProdFound = listProductsRec.Where(p => p.ProductId == thisProdId).FirstOrDefault();
                        }

                        if (thisProdFound != null)
                        {
                            bool insereThisOne = true;
                            prod = thisProdFound;

                            prod.ProductId = thisProdId;
                            prod.ConsommateurIdentiteId = productChoisi.ConsumerId;
                            prod.Count = productChoisi.Count;
                            if (prod.GroupName == "SANS" || prod.GroupName == "CKDO" || prod.GroupName == "")
                                prod.GroupName = "PROD"; // pas de groupe particulier = type PROD


                            if (prod.TypeMontant == 2)
                            {
                               
                                var resultMontant = _basketManager.GetCalculProductByStoredProcedure(structureId, bask.BasketId, identityId, prod.ProductId);

                                if (resultMontant is not null)
                                {
                                    prod.AmountTTCCents = (int)resultMontant;
                                    prod.AmountTTCinCent = (int)resultMontant;
                                    prod.Count = 1;
                                }
                            }


                            if (prod.AmountIsVariable)
                            {
                                if (productChoisi.Amount > 0)
                                {
                                    prod.AmountTTCCents = productChoisi.Amount;
                                    prod.AmountTTCinCent = productChoisi.Amount;
                                    prod.Count = 1;
                                }
                                else
                                {
                                    insereThisOne = false;
                                }
                            }

                            if (insereThisOne)
                            {
                                //Si le produit est configuré avec (un billet par produit) alors on créer autant de ligne que le nombre choisi (nb 3 produits == 3 lignes dans webtracing) 
                                if (prod.UnbilletParProduit)
                                {
                                    _basketManager.InsertMultipleLignesForTheSameProduct(structureId, bask.BasketId, prod);
                                }
                                else
                                {
                                    int r = _basketManager.InsertPanierProduit(structureId, bask.BasketId, prod);
                                }
                            }
                        }
                        else
                        {
                            // ne retrouve pas le produits ?!?
                            Logger.Error(structureId, $"ProductAddBasketFromBasketAjax ({structureId}, {langCode}, {identityId}, {webUserId}, {buyerProfilId}, {listProducts.Length}): can't retrieve product {thisProdId}, event {productChoisi.EventId}, session {productChoisi.SessionId}");
                        }
                    }
                }
                else
                {
                    return Problem("received ProductAddBasketFromBasketAjax.Count == 0 => grmbl !");
                }

                return Ok(bask);
            }
            catch (Exception ex)
            {
                return Problem(ex.Message);
            }
        }

        /// <summary>
        /// produit detail 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="productId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="partnerName"></param>
        /// <param name="htmlSelector"></param>
        /// <returns></returns>
        [Route("ProductDetail/")]
        public IActionResult ProductDetail(int structureId, string langCode, int productId, int identityId, int webUserId, int buyerProfilId, string partnerName, string htmlSelector)
        {

            Logger.Info(structureId, $"ProductDetail ({structureId}, {langCode}, {productId}, {identityId}, {webUserId}, {buyerProfilId}, {partnerName}, {htmlSelector})...");

            try
            {
                PartnerDTO? partner = _partnerManager.GetPartnerInfosByName(partnerName);

                if (partner == null)
                    return BadRequest("Partner doesn't exist");

                string? widgetSignature = HttpContext.Request.Headers["Signature"];

                if (widgetSignature == null)
                    return BadRequest("signature is missing");

                string partnerToken = WidgetSecurityHelper.GeneratePartnerToken(structureId, partner.PartnerName, partner.SecretKey);

                List<string> lstParamsToHash = new()
                {
                    structureId.ToString(),
                    langCode,
                    partnerName,
                    partnerToken,
                    widgetSignature
                };

                string hash = WidgetSecurityHelper.GenerateHash(partner.SecretKey, lstParamsToHash);
                string queryHash = WidgetSecurityHelper.GenerateQueryHash(widgetSignature, partnerToken, partnerName);

                string action = RouteData.Values["action"]?.ToString()!;
                string callProductDetail = $"{action}Hash/{structureId}/{langCode}/{productId}/{identityId}/{webUserId}/{buyerProfilId}/?htmlSelector={htmlSelector}&hash={hash}&queryHash={queryHash}";

                Logger.Info(structureId, $"ProductDetail ({structureId}, {langCode}, {productId}, {identityId}, {webUserId}, {buyerProfilId},  {partnerName}, {htmlSelector}) ok");
                Logger.Trace(structureId, $"ProductDetail ({structureId}, {langCode}, {productId}, {identityId}, {webUserId}, {buyerProfilId},  {partnerName}, {htmlSelector}) ok : {callProductDetail}");

                return Json(new { responseText = callProductDetail });
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// appel du widget produit detail
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="productId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="htmlSelector"></param>
        /// <param name="hash"></param>
        /// <param name="queryHash"></param>
        /// <returns></returns>
        [Route("ProductDetailHash/{structureId}/{langCode}/{productId}/{identityId}/{webUserId}/{buyerProfilId}")]
        public async Task<IActionResult> ProductDetailHash(int structureId, string langCode, int productId, int identityId, int webUserId, int buyerProfilId, string htmlSelector, string hash, string queryHash)
        {
            Logger.Info(structureId, $"ProductHash ({structureId}, {langCode}, {productId}, {identityId}, {webUserId}, {buyerProfilId}, {hash}, {queryHash})...");

            SecurityInfos securityData = WidgetSecurityHelper.GetSecurityInfos(queryHash);

            PartnerDTO? partnerInDB = _partnerManager.GetPartnerInfosByName(securityData.PartnerName);

            if (partnerInDB is null)
                return BadRequest(new { messagr = "Partner doesn't exist" });

            List<string> lstParamsToHash = new()
            {
                structureId.ToString(),
                langCode,
                partnerInDB.PartnerName,
                securityData.RequestPartnerToken,
                securityData.RequestWidgetSignature
            };

            if (!WidgetSecurityHelper.CheckAccessAuthorized(hash, queryHash, lstParamsToHash, partnerInDB.SecretKey))
                return Unauthorized();

            string toHash = $"{RodrigueHttpContext.SousWidgetsUrl}${Request.Method}";
            string signatureCalculee = ApiSignatureManager.GeneratePartnerSignature(toHash, partnerInDB.SecretKey);

            dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");
            ViewBag.SettingsMerge = settingsMerged ?? "";

            BoutiqueDTO bout = _productManager.GetBoutiqueProductDetail(structureId, langCode, productId);

            string returnView = "Index";
            if (bout != null
                && bout.ProductFamiliesList.Count == 1
                && bout.ProductFamiliesList[0].SubFamiliesList.Count == 1
                && bout.ProductFamiliesList[0].SubFamiliesList[0].ProductsList.Count == 1)
            {
                ProductDTO myProd = bout.ProductFamiliesList[0].SubFamiliesList[0].ProductsList[0];
                if (myProd.GroupName == "")
                    myProd.GroupName = "SANS";
                returnView = "detailFor" + myProd.GroupName.ToUpper();
                ViewBag.Boutique = bout;
            }

            BuyerProfilDTO thisBP = new();
            if (buyerProfilId != 0)
            {
                //thisBP = await ApiOffersHelper.GetViaExterne(apiOfferUrl, securityData.RequestPartnerToken, structureId, buyerProfilId);
                thisBP = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId) ?? new();
            }


            ViewBag.TranslationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode);
            //ViewBag.TranslationsList = _translateManager.GetDicoByLangCode(structureId, langCode);;
            ViewBag.BuyerProfil = thisBP;
            ViewBag.HtmlSelector = htmlSelector;
            ViewBag.SignatureWidgetGet = signatureCalculee;
            ViewBag.WCatalogUrl = _configuration["WidgetCatalogUrl"]!;
            ViewBag.WOfferUrl = _configuration["WidgetOfferUrl"]!;
            ViewBag.WCustomerUrl = _configuration["WidgetCustomerUrl"]!;
            ViewBag.ApplicationPath = HttpContext.Request.PathBase;
            ViewBag.StructureId = structureId;
            ViewBag.IdentityId = identityId;
            ViewBag.WebUserId = webUserId;
            ViewBag.BuyerProfilId = buyerProfilId;
            ViewBag.PartnerToken = securityData.RequestPartnerToken;
            ViewBag.LangCode = langCode;
            ViewBag.DeviseCode = WidgetUtilitiesHelper.GetDeviseCode(structureId);

            return View(returnView);
        }



    }
}
