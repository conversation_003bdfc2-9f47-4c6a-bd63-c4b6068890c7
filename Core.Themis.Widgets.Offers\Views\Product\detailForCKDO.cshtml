﻿@using System.Globalization;
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@using Core.Themis.Widgets.Offers.Helpers
@*************** VARIABLES *************@
@{
    ViewBag.Title = "Product Gift Card";

    string UrlToPath = $"{ @Context.Request.Scheme }://{ @Context.Request.Host}{ @Context.Request.PathBase }";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }
    List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO> TranslationsList = ViewBag.TranslationsList as List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO>;
}

@*************** STYLES *************@
<link href="@(UrlToPath)css/bootstrap-spinner/bootstrap-input-spinner.css" rel="stylesheet" />

@if (ViewBag.Boutique != null)
{
    foreach (var productFamily in ViewBag.Boutique.ProductFamiliesList)
    {
        foreach (var productSubFamily in productFamily.SubFamiliesList)
        {
            foreach (var product in productSubFamily.ProductsList)
            {
                <div class="row mb-3" id="bel_productdetail" data-productid="@(product.ProductId)" data-amount="@(product.AmountTTCinCent)" data-nbmin="@(product.MinSellable)" data-nbmax="@(product.MaxSellable)" data-amountisvariable="@(product.AmountIsVariable.ToString().ToLower())" data-step="@(product.Step.ToString().Replace(",","."))" data-minamount="@(product.MinSellable)" data-maxamount="@(product.MaxSellable)">
                    <!-- img start -->
                    <div class="col-md-4 col-lg-3">
                        @{
                            string imgSrc = "";
                            if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                imgSrc = product.ImageUrl;
                            }
                            else
                            {
                                imgSrc = UrlToPath + "images/noimage.png";
                            }
                        }
                        <div class="bel_product_img">
                            <img src="@imgSrc" alt="@(product.ProductName)" class="img-fluid" />
                            <span class="bel_product_price" data-pricetoformat="@(product.AmountTTCinCent)">
                                @if (product.AmountIsVariable)
                                {
                                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblVariableAmount")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblVariableAmount") : "Montant variable")
                                }
                                else
                                {
                                    @if (ViewBag.DeviseCode.IsBefore)
                                    {
                                        @Html.Raw(ViewBag.DeviseCode.Code) @((double)product.AmountTTCinCent / 100)
                                    }
                                    else
                                    {
                                        @((double)product.AmountTTCinCent / 100) @Html.Raw(ViewBag.DeviseCode.Code)
                                    }
                                }
                            </span>
                        </div>
                    </div>
                    <!-- img end -->
                    <!-- content start -->
                    <div class="col-md-8">
                        <h3 class="bel_product_title">@(product.ProductName)</h3>
                        @if (ViewBag.SettingsMerge.products.showReference.CKDO == "True")
                        {
                            <div class="bel_product_ref"><span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblReferenceDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblReferenceDot") : "Référence :") </span>@(product.ProductCode)</div>
                        }

                        @if (!string.IsNullOrEmpty(product.ProductDescription))
                        {
                            <div class="bel_product_description">@Html.Raw(product.ProductDescription)</div>
                        }

                        @if (!string.IsNullOrEmpty(product.ProductDescriptionLong))
                        {
                            <div class="bel_product_description_long">@Html.Raw(product.ProductDescriptionLong)</div>
                        }

                        @if (product.AmountIsVariable)
                        {
                            <!-- Variable amount gift card -->
                            <div class="mt-3">
                                <label for="giftCardAmount" class="form-label">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblAmount")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblAmount") : "Montant")</label>
                                <div class="input-group">
                                    @if (ViewBag.DeviseCode.IsBefore)
                                    {
                                        <span class="input-group-text">@Html.Raw(ViewBag.DeviseCode.Code)</span>
                                    }
                                    <input type="number" class="form-control" id="giftCardAmount" min="@(((double)product.MinSellable / 100).ToString("0.00").Replace(",", "."))" max="@(((double)product.MaxSellable / 100).ToString("0.00").Replace(",", "."))" step="@(product.Step.ToString().Replace(",", "."))" value="@(((double)product.MinSellable / 100).ToString("0.00").Replace(",", "."))" data-productid="@product.ProductId" />
                                    @if (!ViewBag.DeviseCode.IsBefore)
                                    {
                                        <span class="input-group-text">@Html.Raw(ViewBag.DeviseCode.Code)</span>
                                    }
                                </div>
                                <div class="form-text">
                                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblAmountRange")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblAmountRange") : "Montant entre")
                                    @if (ViewBag.DeviseCode.IsBefore)
                                    {
                                        @Html.Raw(ViewBag.DeviseCode.Code) @((double)product.MinSellable / 100)
                                    }
                                    else
                                    {
                                        @((double)product.MinSellable / 100) @Html.Raw(ViewBag.DeviseCode.Code)
                                    }
                                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblAnd")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblAnd") : "et")
                                    @if (ViewBag.DeviseCode.IsBefore)
                                    {
                                        @Html.Raw(ViewBag.DeviseCode.Code) @((double)product.MaxSellable / 100)
                                    }
                                    else
                                    {
                                        @((double)product.MaxSellable / 100) @Html.Raw(ViewBag.DeviseCode.Code)
                                    }
                                </div>
                            </div>

                            <!-- Quantity for variable amount -->
                            <div class="mt-3">
                                <label for="giftCardQuantity" class="form-label">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblQuantity")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblQuantity") : "Quantité")</label>
                                <input type="number" class="form-control" id="giftCardQuantity" min="1" max="@(product.MaxSellable)" value="1" />
                            </div>
                        }
                        else
                        {
                            <!-- Fixed amount gift card -->
                            <div class="mt-3">
                                <label for="giftCardQuantity" class="form-label">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblQuantity")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_LblQuantity") : "Quantité")</label>
                                <input type="number" class="form-control" id="giftCardQuantity" min="@(product.MinSellable)" max="@(product.MaxSellable)" value="@(product.MinSellable)" />
                            </div>
                        }

                        <!-- add to basket start -->
                        <div class="mt-4">
                            <button class="btn btn-primary" id="addGiftCardToBasket" type="button">
                                @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnAddToBasket")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnAddToBasket") : "Ajouter à votre panier")
                                : <span class="totalAmount" data-pricetoformat="@(product.AmountTTCinCent)">
                                    @if (ViewBag.DeviseCode.IsBefore)
                                    {
                                        @Html.Raw(ViewBag.DeviseCode.Code) @((double)product.AmountTTCinCent / 100)
                                    }
                                    else
                                    {
                                        @((double)product.AmountTTCinCent / 100) @Html.Raw(ViewBag.DeviseCode.Code)
                                    }
                                </span>
                            </button>
                        </div>
                        <!-- add to basket end -->
                    </div>
                    <!-- content end -->
                </div>
            }
        }
    }

    <!-- Modal for continue shopping -->
    <div class="modal fade" id="modalAskContinueShoppingProduct" tabindex="-1" aria-labelledby="modalAskContinueShoppingProductLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalAskContinueShoppingProductLabel">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_ModalTitle")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_ModalTitle") : "Produit ajouté")</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_ModalMessage")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_ModalMessage") : "Votre carte cadeau a été ajoutée au panier.")</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="BtnContinueShoppingProducts">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_BtnContinueProducts")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_BtnContinueProducts") : "Continuer mes achats")</button>
                    <button type="button" class="btn btn-outline-secondary" id="BtnContinueShoppingEvents">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_BtnContinueEvents")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_BtnContinueEvents") : "Voir les spectacles")</button>
                    <button type="button" class="btn btn-primary" id="BtnGoToBasket">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_BtnGoToBasket")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Product_ProductDetails_BtnGoToBasket") : "Voir mon panier")</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        var apiToken = "@ViewBag.Token";
        var widgetSignature = "@ViewBag.WidgetSignature";
        var widgetSignatureCallsGet = "@ViewBag.SignatureWidgetGet";
        var structureId = parseInt("@ViewBag.StructureId");
        var langCode = "@ViewBag.LangCode";
        var htmlSelector = "@ViewBag.HtmlSelector";

        var identityId = @Html.Raw(ViewBag.IdentityId);
        var webUserId = @Html.Raw(ViewBag.WebUserId);
        var buyerProfilId = @Html.Raw(ViewBag.BuyerProfilId);
        var partnerToken = "@ViewBag.PartnerToken";
        var deviseCode = @Html.Raw(Json.Serialize(ViewBag.DeviseCode));
        var Boutique = @Html.Raw(Json.Serialize(ViewBag.Boutique));
    </script>
    <script src="@(UrlToPath)js/bootstrap-spinner/bootstrap-input-spinner.js"></script>
    <script src="@(ViewBag.CustomPackageUrl)"></script>
    <script src="@(UrlToPath)js/Product/productDetails/productDetailsCKDO.js"></script>
}
