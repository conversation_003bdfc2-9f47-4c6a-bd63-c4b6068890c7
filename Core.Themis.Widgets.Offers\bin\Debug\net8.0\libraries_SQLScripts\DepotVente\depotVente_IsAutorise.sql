﻿

/*
declare @pEventId int = 1
declare @pOrderId int = 1
declare @pSeatId int = 1
declare @pIdentityId int = 1
*/

 declare @sql nvarchar(max)


 declare @type_tarif_id int
 declare @categorie_id int
 declare @seance_id int
 set @sql ='select top 1 @type_tarif_id=type_tarif_id, @categorie_id=categorie_id, @seance_id=esvg.seance_id FROM dossiersvg_' + LTRIM(STR(@pEventId)) + ' dsvg
 inner join entreesvg_' + LTRIM(STR(@pEventId)) + ' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v
 where entree_id=' + LTRIM(STR(@pSeatId)) +
 'AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@pEventId)) + ' esvg2 
 			WHERE 
 				 esvg.entree_id=esvg2.entree_id	and esvg2.dossier_id=esvg.dossier_id
 				 and esvg.seance_id=esvg2.seance_Id
 				)';			
 exec sp_executesql @sql, N'@type_tarif_id int out, @categorie_id int out, @seance_id int out', @type_tarif_id out , @categorie_id out, @seance_id out
 
 --select @type_tarif_id as priceidvoulue, @categorie_id as categidvoulue, @seance_id as seanceidvoulue, @pEventId as manifidvoulue
 
 /* get categ, tarif, manif_id, etc... des places dejÃ  mis */
 create table #TmpEntree (
 manifestation_id int,
 session_id int,
 type_tarif_id int,
 categorie_id int,
 nbr int
 )
 
 declare @pEventIdDV int, @commandeidDV int,@dossieridDV int
 DECLARE complex_cursor CURSOR FOR
 			SELECT 
 			distinct manif_id , commande_id, dossier_id
 			from depotvente_misesenvente dv 
 			where dv.identite_id=@pIdentityId --and cl.manifestation_id<>0
 		OPEN complex_cursor;
 	FETCH  NEXT FROM complex_cursor INTO @pEventIdDV,@commandeidDV,@dossieridDV;
 
 	WHILE(@@FETCH_STATUS=0)
 		BEGIN	
 		
 SET @SQL ='INSERT INTO #TmpEntree SELECT distinct
 		s.manifestation_id, s.seance_id,
 		 tt.type_tarif_id, c.categ_id, count(*)		 
 		 FROM dossiersvg_' + LTRIM(STR(@pEventIdDV)) + ' dsvg
 			inner join entreesvg_' + LTRIM(STR(@pEventIdDV)) + ' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v
 			inner join seance s on esvg.seance_id=s.seance_Id
 			inner join manifestation m on m.manifestation_id=s.manifestation_id and m.manifestation_id=' + LTRIM(STR(@pEventIdDV)) + '
 			inner join type_tarif tt on esvg.type_tarif_id=tt.type_tarif_id
 			inner join categorie c on esvg.categorie_id =c.categ_id
 			inner join entree_' + LTRIM(STR(@pEventIdDV)) + ' e ON esvg.entree_id=e.entree_id
 			inner join commande cmd ON cmd.commande_id=dsvg.commande_id		
 			inner join commande_ligne cmdl ON cmdl.commande_id=cmd.commande_id AND cmdl.dossier_id=dsvg.dossier_id  and cmdl.seance_id=s.seance_Id	
 			inner join REFERENCE_LIEU_PHYSIQUE R on REFERENCE_UNIQUE_PHYSIQUE_ID = REF_UNIQ_PHY_ID
 			inner join DENOMINATION D on R.DENOMINATION_ID = D.DENOM_ID
 	inner join depotvente_misesenvente dv on dv.entree_id=esvg.entree_id and dv.identite_id=dsvg.identite_id
 	and dv.seance_id=esvg.seance_id and dv.manif_id =s.manifestation_id				
 	left outer join depotvente_statut dvs on dvs.entree_id=esvg.entree_id and dvs.identite_id=dsvg.identite_id
 	and dvs.seance_id=esvg.seance_id and dvs.manif_id =s.manifestation_id				
 			WHERE dsvg.dossier_id='  + LTRIM(STR(@dossieridDV)) + ' AND cmd.commande_id=' + LTRIM(STR(@commandeidDV))
 			+ ' AND type_ligne=''DOS'' 
 			
 					AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@pEventIdDV)) + ' esvg2 
 			--on esvg2.dossier_id=dsvg.dossier_id
 			WHERE --dsvg2.dossier_id  =dsvg.dossier_id and
 				 esvg.entree_id=esvg2.entree_id	and esvg2.dossier_id=esvg.dossier_id
 				  and esvg.seance_id=s.seance_Id
 				 ) 
 			AND (dsvg.dossier_etat <> ''P'' or dsvg.type_operation <> ''EDIT'')
 			AND dsvg.identite_id=' +  LTRIM(STR(@pIdentityId)) + '
 			group by s.manifestation_id,s.seance_id,
 		 tt.type_tarif_id, c.categ_id
 			'
 			
 		--PRINT @SQL
 		EXEC (@SQL)		
 		
 		--select @pEventIdDV
 		
 		FETCH  NEXT FROM complex_cursor INTO @pEventIdDV,@commandeidDV,@dossieridDV;
 		END	
 CLOSE complex_cursor;
 DEALLOCATE complex_cursor;
 --SELECT * FROM #TmpEntree
 
 declare @myinterval int, @mynbr int
 select @myinterval =datediff(HOUR,GETDATE(), s.seance_date_deb) from seance s where seance_Id=@seance_id
 
 /* **************** moulinage des regles */
 
 declare @checked int
 
 declare @nmax int, @interval_debut int, @interval_fin int
 
 declare @sql_where varchar(1000) 
 declare @labelregle varchar(50)
 declare @defaultmax int
 DECLARE contrainte_cursor CURSOR SCROLL FOR 
 	select sql, label,default_max from depot_vente_regles
 	
 	OPEN contrainte_cursor
 	FETCH NEXT FROM contrainte_cursor INTO @sql_where,@labelregle,@defaultmax
 	WHILE @@FETCH_STATUS=0
 	BEGIN 
 		
 		DECLARE @sql_to_exec nvarchar(1000) 
 		set @nmax = @defaultmax 
 			-- au cas ou la regle pour mon tarif n'existe pas dans depotvente_contraintes, @nmax est initialisÃ© (val=0 les parametre de regle doivent exister, val=999 regle peut ne pas exister
 			-- ex, je demande le tarif 9940001 : il n'y a pas de regle avec le tarif 994001 et @defaultmax de depot_vente_regles=0 -> je ne peux pas
 			-- ex, je demande la categ 10001 : il n'y a pas de regle avec le categ 10001 et @defaultmax de depot_vente_regles=999 -> je peux remettre quand mÃªme
 		
 		SET @sql_to_exec='select @nmax=nmax, @interval_debut=interval_debut, @interval_fin=interval_fin from depotvente_contraintes where ' 
 			+ REPLACE(REPLACE(REPLACE(REPLACE(@sql_where,'@type_tarif_id',@type_tarif_id), 
 				'@categorie_id',@categorie_id),
 				'@pEventId',@pEventId),
 				'@SEANCEID',@seance_id)
 		print @sql_to_exec;
 		--exec sp_executesql @sql, N'@type_tarif_id int out, @categorie_id int out, @seance_id int out', @type_tarif_id out , @categorie_id out, @seance_id out
 
 		exec sp_executesql @sql_to_exec,  N'@nmax int out, @interval_debut int out, @interval_fin int out', @nmax out, @interval_debut out, @interval_fin out
 		
 		--select @nmax
 		if (@myinterval>@interval_debut or @myinterval<@interval_fin)
 		begin
 			print 'contrainte ' + @labelregle + ' date non verifiÃ©e'
 	        select convert(bit, 0)
 		end
 		set @mynbr =0
 		--select @mynbr= SUM(nbr) from #TmpEntree where priceid =@type_tarif_id
 		SET @sql_to_exec='select @mynbr= SUM(nbr) from #TmpEntree where  ' 
 				+ REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(@sql_where,'@type_tarif_id',@type_tarif_id), 
 				'@categorie_id',@categorie_id),
 				'@pEventId',@pEventId),
 				'@SEANCEID',@seance_id),
 				'and manifestation_id is null',''),
 				'and session_id is null',''),
 				'and categorie_id is null',''),
 				'type_tarif_id is null','1=1')
		
 		exec sp_executesql @sql_to_exec,  N'@mynbr int out', @mynbr out
 						
 		print @sql_to_exec;
 		
 		if (@mynbr is null) set @mynbr =0
 		print '@mynbr=' + convert(varchar,@mynbr) + ' nmax=' + convert(varchar,@nmax)
 		
 		if (@mynbr+1>@nmax)
 		begin
 			print 'contrainte ' + @labelregle + ' max nbr (' + convert(varchar,@mynbr+1) + '>' + convert(varchar,@nmax) + ') non verifiÃ©e'
 			
 			CLOSE contrainte_cursor
 			DEALLOCATE contrainte_cursor
            select convert(bit, 0)
 		end
  		FETCH NEXT FROM contrainte_cursor INTO @sql_where,@labelregle,@defaultmax
 	END
 	CLOSE contrainte_cursor
 	DEALLOCATE contrainte_cursor
 
 print 'Ok!'
 select convert(bit, 1)