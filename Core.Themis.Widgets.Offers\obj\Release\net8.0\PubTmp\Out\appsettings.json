{
  "Mode": "Prod",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    }
  },
  "ConnectionStrings": {
    "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    //"GlobalOpinionDB": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "SitePaiement": "https://payment.themisweb.fr/{structureId}/payment/optionspayment/{structureId}/[basketid]/[platformname]/[lang]/[haskey]/[identiteid]",

  //charge les traductions filtrées par areas
  "TranslationsAreas": {
    "CrossSelling": [ "Global", "CrossSelling" ],
    "Session": [ "Global", "Session" ],
    "Basket": [ "Global", "Basket" ],
    "Product": [ "Global", "Product" ],
    "HomeModular": [ "Global", "HomeModular" ],
    "Insurance": [ "Global", "Insurance" ],
    "EventsCatalog": [ "Global", "Catalog" ]
  },
  "Cache": {
    //Cache pour la liste des manifestation en secondes
    //"EventsAbsoluteExpiration": 120,
    //"EventsSlidingExpiration": 2,
    //Cache pour la liste des seances (calendrier) en secondes
    "SessionsAbsoluteExpiration": 120,
    "SessionsSlidingExpiration": 2,
    //Cache de la Grille de tarif � chaque changement de zone, etage, section en secondes
    //"GrilleTarifAbsoluteExpiration": 120,
    //"GrilleTarifSlidingExpiration": 2,
    "AdhesionsAbsoluteExpiration": 120,
    "AdhesionsSlidingExpiration": 2,

    "TranslationsAbsoluteExpiration": 10,
    "SponsorsAbsoluteExpiration": 10,
    "InsuranceAbsoluteExpiration": 10,
    "HomeModularAbsoluteExpiration": 120,
    //Cache pour la liste des sièges en secondes
    "SeatsAbsoluteExpiration": 120,
    "SeatsSlidingExpiration": 2,
    "SeatsTextAbsoluteExpiration": 600,
    "SeatsTextSlidingExpiration": 2,
    "ProductsGlobauxToBasket": 2400,
    "ProductsForEvents": 2400,
    "ProductsForSessions": 2400,


    //Cache pour la liste des manifestations (eventsCatalog) en secondes
    "EventsCatalogSlidingExpiration": 600,
    "EventsCatalogAbsoluteExpiration": 300,
    //Cache pour la liste des manifestations (eventsCatalog) au niveau des filtres en secondes
    "EventsCatalogAFiltersbsoluteExpiration": 600,
    "EventsCatalogFilterSlidingExpiration": 300,

    "UpdateCacheFile": "\\\\Srv-paiement64\\CUSTOMERFILES\\PROD\\updatecache.txt"
  },

  "HomeModular": {
    "Products_All_Url": "https://indiv.{domain}/{structureId}/BoutiqueHome.aspx?idstructure={structureId}",
    "Products_Family_Url": "https://indiv.{domain}/{structureId}/boutiqueFamille.aspx?idstructure={structureId}&fid={familyId}",
    "Products_Subfamily_Url": "https://indiv.{domain}/{structureId}/boutiqueSousFamille.aspx?idstructure={structureId}&sfid={subfamilyId}",
    "Product_One_Url": "https://indiv.{domain}/{structureId}/boutiqueProduitDetailWidget.aspx?idstructure={structureId}&pid={productId}",
    "Events_All_Url": "https://indiv.{domain}/{structureId}/fListeManifs.aspx?idstructure={structureId}",
    "Events_Genre_Url": "https://indiv.{domain}/{structureId}/fListeManifs.aspx?idstructure={structureId}&genreid={genreId}",
    "Events_Subgenre_Url": "https://indiv.{domain}/{structureId}/fListeManifs.aspx?idstructure={structureId}&subgenreid={subgenreId}",
    "Events_Group_Url": "https://indiv.{domain}/{structureId}/fListeManifs.aspx?idstructure={structureId}&groupid={groupId}",
    "Event_One_Url": "https://indiv.{domain}/{structureId}/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}&sessionid={sessionId}",
    "Subscription_Url": "https://abo.{domain}/{structureId}/home/<USER>/{langCode}",
    "Events_Featured_Url": "https://indiv.{domain}/{structureId}/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}",
    "Products_Featured_Url": "https://indiv.{domain}/boutiqueProduitDetailWidget.aspx?idstructure={structureId}&pid={productId}",
    "CustomerArea_Url": "https://customer.{domain}/{structureId}/home.aspx?idstructure={structureId}"
  },

  "EventsCatalog": {
    "Event_One_Url": "https://indiv.{domain}/{structureId}/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}",
    "Abo_One_Url": "https://indiv.{domain}/{structureId}/fChoixSeanceAbo.aspx?idstructure={structureId}&FormulaId={formulaId}",
    "WaitingList_Url": "https://customer.{domain}/{structureId}/WaitList.aspx?idstructure={structureId}&page=waitlist&EventId={eventId}&disconnect=0&iswidget=1"
  },



  "ApiAuthenticationUrl": "http://back-themis-ws/WS/API/AUTHENTICATIONS/v110/api/",
  "identiteSalt": "RodWebShop95",
  "WidgetCatalogUrl": "https://widgets.themisweb.fr/catalog/v202/",
  "WidgetOfferUrl": "https://widgets.themisweb.fr/offers/v203/",
  "WidgetCustomerUrl": "https://widgets.themisweb.fr/customers/v202/",

  "TypeRun": "PROD",
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  //"PathForSqlScript": "..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "PathForSqlScriptDLDLDLDLD": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",

  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\PROD\\{structureId}\\CONFIGSERVER\\config.ini.xml",
  "RodriguePartnerName": "RODRIGUE",

  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "AudienceToken": "ThemisAPI",
  "CryptoKey": "RodWebShop95",
  "PathBase": "/offers/v203/",
  "SeatPlan": {
    "SeatingPlanPerspectivePhysicalPath": "\\\\**************\\customerfiles\\PROD\\{structureId}\\INDIV\\IMAGES\\seatingplans\\iindexToXY_perspect[param].xml",
    "SeatingPlanUrlPath": "https://indiv.themisweb.fr/files/{structureId}/INDIV/images/seatingplans/"
  },

  "Images": {
    "PanoUrlPath": "https://indiv.themisweb.fr/files/{structureId}/INDIV/images/pano/",
    "BaseImagesPhysicalPath": "\\\\**************\\customerfiles\\PROD\\{structureId}\\INDIV\\IMAGES\\",
    "EventsImagesPhysicalPath": "\\\\**************\\customerfiles\\PROD\\{structureId}\\INDIV\\IMAGES\\manifestations\\",
    "EventsImagesUrlPath": "https://indiv.themisweb.fr/{structureId}/files/{structureId}/INDIV/images/manifestations/",
    "ProductsImagesPhysicalPath": "\\\\**************\\customerfiles\\PROD\\{structureId}\\INDIV\\IMAGES\\produits\\",
    "ProductsImagesUrlPath": "https://indiv.themisweb.fr/files/{structureId}/files/{structureId}/images/produits/",
    "BaseImagesUrlPath": "/files/{structureId}/INDIV/images/",
    "AssetsImagesUrlPath": "https://indiv.themisweb.fr/assets/IMAGES/indiv/"
  },
  "AssetsUrlPath": "https://indiv.themisweb.fr/assets/",
  "PanoFileVersion": "pano1.20.9.js",
  "physicalPathOfSettingsJSON": "\\\\**************\\customerfiles\\PROD\\{structureId}{plateformCode}\\appsettings.json",
  "CustomerfilesIndivPhysicalPath": "\\\\**************\\customerfiles\\PROD\\{structureId}\\INDIV\\",
  "CustomerfilesUrlPath": "https://indiv.themisweb.fr/files/{structureId}/INDIV/",
  "Sponsor": {
    "SponsorAdvantageCardSalt": "7f5a54f374604d362034ba883d9ac38dc4a8f00f8dc708138135d242457a4e49"
  }
}
