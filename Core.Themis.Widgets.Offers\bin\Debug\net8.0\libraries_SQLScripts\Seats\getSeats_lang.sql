/* getSeats.sql */

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT convert(int,entree_id) as seat_id,
convert(int,e.seance_id) as session_id,
categ_id, 
cat.categ_nom as categ_name,
entree_etat as currentstate,
entree_etat,
e.reserve_id as reserve_id,
flag_selection,
rlp.rang as rank,
rlp.siege as seat,
rlp.zone_id,
z.zone_nom as zone_name
, rlp.etage_id as floor_id
, etag.etage_nom as floor_name
, rlp.section_id, 
sect.section_nom as section_name,
denom.denom_id as denom_id,

CASE WHEN tdenom.denom_nom is null then denom.denom_nom else tdenom.denom_nom end as denom_name,

rlp.orientation,
rlp.pos_x, rlp.pos_y, rlp.decal_x, rlp.decal_y,
 rlp.iindex, 
 rlp.type_siege as type_seat

FROM entree_[eventID] e
INNER JOIN seance s on s.seance_Id = e.seance_id
INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
INNER JOIN categorie cat ON cat.categ_id = e.categorie_id
INNER JOIN reference_lieu_physique rlp ON e.iindex = rlp.iindex and e.reference_unique_physique_id = rlp.ref_uniq_phy_id
INNER JOIN zone z ON z.zone_id = rlp.zone_id
INNER JOIN etage etag ON etag.etage_id = rlp.etage_id
INNER JOIN section sect ON sect.section_id = rlp.section_id
INNER JOIN denomination denom on denom.denom_id = rlp.denomination_id
left join traduction_denomination tdenom on tdenom.denom_id = denom.denom_id and tdenom.langue_id = @LgId


WHERE e.entree_id IN ({seatsids})