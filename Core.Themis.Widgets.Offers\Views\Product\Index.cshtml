﻿@using System.Globalization;
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@using Core.Themis.Widgets.Offers.Helpers
@*************** VARIABLES *************@
@{
    ViewBag.Title = "Produit";
    string UrlToPath = $"{ @Context.Request.Scheme }://{ @Context.Request.Host}{ @Context.Request.PathBase }";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }
    List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO> TranslationsList = ViewBag.TranslationsList as List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO>;
}
@*************** STYLES **************@
@section styles {
    <link rel="stylesheet/less" type="text/css" href="@(UrlToPath)css/Product/style.less">
}

<div id="productWrapper">
    <span>
        @ViewBag.Boutique.ProductFamiliesList[0].FamilyName,
        @ViewBag.Boutique.ProductFamiliesList[0].SubFamiliesList[0].SubFamilyName,
        @ViewBag.Boutique.ProductFamiliesList[0].SubFamiliesList[0].ProductsList[0].ProductName !
    </span>
</div>

@*************** SCRIPTS *************@
@section scripts {
    <script>
            var TranslationsList = @Html.Raw(Json.Serialize(TranslationsList));
            var Basket =  @Html.Raw(Json.Serialize(ViewBag.Basket));

            var widgetOfferUrl = "@ViewBag.WOfferUrl";
            var widgetCatalogUrl = "@ViewBag.WCatalogUrl";
            var widgetCustomerUrl = "@ViewBag.WCustomerUrl";

            var apiToken = "@ViewBag.Token";
            var widgetSignature = "@ViewBag.WidgetSignature";
            var widgetSignatureCallsGet = "@ViewBag.SignatureWidgetGet";
            var structureId = parseInt("@ViewBag.StructureId");
            var langCode = "@ViewBag.LangCode";
            var htmlSelector = "@ViewBag.HtmlSelector";

            var identityId =@Html.Raw(ViewBag.IdentityId);
            var webUserId =@Html.Raw(ViewBag.WebUserId);
            var buyerProfilId =@Html.Raw(ViewBag.BuyerProfilId);
            var partnerToken = "@ViewBag.PartnerToken";
            var deviseCode = @Html.Raw(Json.Serialize(ViewBag.DeviseCode));

            var Boutique = @Html.Raw(Json.Serialize(ViewBag.Boutique))
    </script>
    <script src="@(UrlToPath)js/Product/product.js"></script>

}


