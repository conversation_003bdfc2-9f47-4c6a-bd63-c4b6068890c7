﻿/*  
declare @pidentiteId int;
declare @pcommandeId int
--declare @pformuleId varchar(100) = null;
declare @pmanifId int
declare @pseanceId int
declare @pentreeId int
 

 set @pidentiteId = 473
 set @pmanifId = 9
 set @pcommandeId = 813
 set @pseanceId = 11
 set @pentreeId =487

 */
 
 -- ********************* get Historique d'une commande ***************

create table #TmpEntree (
structurename varchar(max),
eventname varchar(50),
sessiondatedeb datetime,
sessiondatefin datetime, 
eventid int,
orderid int,
dossierid int,
priceid int,
pricename varchar(50) ,
categoryid int,
categoryname varchar(50),
sectionid int,
sectionname varchar(50),
sectioncode varchar(100),
zoneid int,
zonename varchar(50),
zonecode varchar(100),
etageid int,
etagename varchar(50),
etagecode varchar(100),
--type<PERSON><PERSON> varchar(10),
denom  varchar(50),
rank varchar(50), 
seat varchar(50)
--,amount int
,etat varchar(1)
,entreeid int
,seanceid int
,aboid int
,formuleid int
,formulenom varchar(50)
,lieuid int
,lieuname varchar(50)
--,depotventeid int
--,status_reprise varchar(10)
,identiteconsommateurid int --identite relie sur la place (consommateur)
,identitedossierid int --identite du dossier (aboV2)
,identitedossiernom varchar(50) --nom du dossier (aboV2)
,identitedossierprenom varchar(50) --prenom du dossier (aboV2)
,identiteid int
--, maquette_id_rod int
,notnumbered int
--,priseauto int -- pas utilise
--,vueplacement int  -- pas utilise
--,choixplan int -- pas utilise
, isControlAccessPassed bit
, numeroBillet varchar(max)
--,typeMaquette varchar(max) --Type de maquette 'CARTEWEB'
--,maquette_id_carte_web int
--,nombre_abo int
--,seance_id_ref_carte_web int
)
  


DECLARE @SEANCE_ID INT, @MANIFID INT,@DOSSIERID INT, @COMMANDEID INT,@SEANCE_DATE_DEB datetime
DECLARE @SQLCurs nvarchar(max)
		
		SET @SQLCurs ='Declare complex_cursor CURSOR GLOBAL FOR 
		SELECT 
		distinct  s.seance_Id, s.manifestation_id, c.commande_id,dossier_id ,s.seance_date_deb  from commande c
		inner join commande_ligne cl ON cl.commande_id=c.commande_id	
		inner join seance s on s.seance_Id=cl.seance_id and cl.manifestation_id=s.manifestation_id 
		where c.identite_id='+CONVERT(varchar(200), @pidentiteId)+' ' -- and cl.commande_id ='+@pCommandeId+' ' --	and s.manifestation_id = 4  and s.seance_id = 4	
		
		
		if (@pcommandeId<>'')
		begin
			set @SQLCurs = @SQLCurs + ' and cl.commande_id =' + CONVERT(varchar(200), @pcommandeId) + ' ';
		end

		if (@pmanifId<>'')
		begin
			set @SQLCurs = @SQLCurs + ' and s.manifestation_id in (' +CONVERT(varchar(200), @pmanifId)   + ')';
		end
			   		
		if (@pseanceId<>'')
		begin
			set @SQLCurs = @SQLCurs + ' and s.seance_id in (' + CONVERT(varchar(200), @pseanceId) + ')';
		end
		
		set @SQLCurs = @SQLCurs + 'and cl.type_ligne=''DOS'' '
  print @SQLCurs
		

 exec sp_executesql @SQLCurs

OPEN complex_cursor;
FETCH  NEXT FROM complex_cursor INTO @SEANCE_ID, @MANIFID, @COMMANDEID,@DOSSIERID,@SEANCE_DATE_DEB;
while(@@FETCH_STATUS=0)
	BEGIN
	DECLARE @SQL VARCHAR(max)
	IF (@MANIFID<>0)
	BEGIN

	SET @SQL ='
		DECLARE @const_placementlibre int; set @const_placementlibre =32;
		DECLARE @const_priseauto int; set @const_priseauto =16;
		DECLARE @const_vueplacement int; set @const_vueplacement =8;
		DECLARE @const_choixsurplan int; set @const_choixsurplan =1;
		
		INSERT INTO #TmpEntree SELECT distinct  struc.structure_nom, m.manifestation_nom, 
		s.seance_date_deb,s.seance_date_fin, s.manifestation_id,
		cmd.commande_id, dsvg.dossier_id,tt.type_tarif_id, tt.type_tarif_nom, c.categ_id, c.categ_nom,
		
		r.section_id,
		section.section_nom,
		section.section_code,
		r.zone_id,
		zone.zone_nom,
		zone.zone_code,
		r.etage_id,
		etage.etage_nom,
		etage.etage_code
		,d.denom_nom, r.rang, r.siege,

		esvg.entree_etat, e.entree_id, e.seance_id, cmdl.abo_id, 
		abo.formule_id
		,form_abon_nom
		,l.lieu_id, lieu_nom
		, case when consommateur_id IS NULL  then ''0'' else consommateur_id end
		,dsvg.identite_id
		,identDoss.identite_nom
		,identDoss.identite_prenom
		,cmdl.identite_id
		,  notnumbered = case WHEN gp.prise_place is null 
				then 
					0 
				else 
					CASE WHEN (gp.prise_place & @const_placementlibre)=0 THEN 0 ELSE 1 END 
				end
 --,priseauto = CASE WHEN (gp.prise_place & @const_priseauto)=0 THEN 0 ELSE 1 END
 --,vueplacement = CASE WHEN (gp.prise_place & @const_vueplacement)=0 THEN 0 ELSE 1 END
 --,choixplan = CASE WHEN (gp.prise_place & @const_choixsurplan)=0 THEN 0 ELSE 1 END
, case when ca.id is null then 0 else 1 end as controlAccessPassed


, (SELECT case when RTRIM(LTRIM(motif)) = '''' then RTRIM(LTRIM(externe)) else RTRIM(LTRIM(motif)) end
    FROM recette WHERE manifestation_id='+ LTRIM(STR(@MANIFID)) +'
    AND dossier_id='  + LTRIM(STR(@DOSSIERID)) + '
 AND entree_id=' + STR(@pentreeId) +' ) as num 

		 FROM dossiersvg_' + LTRIM(STR(@MANIFID)) + ' dsvg
			inner join entreesvg_' + LTRIM(STR(@MANIFID)) + ' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v
			inner join structure struc on 1=1
			inner join seance s on esvg.seance_id=s.seance_Id
			inner join manifestation m on m.manifestation_id=s.manifestation_id and m.manifestation_id=' + LTRIM(STR(@MANIFID)) + '
			inner join type_tarif tt on esvg.type_tarif_id=tt.type_tarif_id
			inner join categorie c on esvg.categorie_id =c.categ_id
			inner join entree_' + LTRIM(STR(@MANIFID)) + ' e ON esvg.entree_id=e.entree_id AND e.entree_id= ' + STR(@pentreeId) +' 
			inner join commande cmd ON cmd.commande_id=dsvg.commande_id		
			inner join commande_ligne cmdl ON cmdl.commande_id=cmd.commande_id AND cmdl.dossier_id=dsvg.dossier_id  and cmdl.seance_id=s.seance_Id	
			left outer join abonnement abo on abo.abo_id =cmdl.abo_id
			left outer join  formule_abonnement fa on fa.form_abon_id = abo.formule_id
			inner join REFERENCE_LIEU_PHYSIQUE R on REFERENCE_UNIQUE_PHYSIQUE_ID = REF_UNIQ_PHY_ID
			inner join zone on zone.zone_id = r.zone_id
			inner join etage on etage.etage_id = r.etage_id
			inner join section on section.section_id = r.section_id
			inner join DENOMINATION D on R.DENOMINATION_ID = D.DENOM_ID
			inner join lieu l on l.lieu_id = s.lieu_id
			LEFT OUTER JOIN Entree_Complement EC on EC.seance_id = s.seance_Id and EC.dossier_id = dsvg.dossier_id and EC.entree_id = e.entree_id
			LEFT OUTER JOIN depotvente_misesenvente dv on dv.entree_id=esvg.entree_id and dv.identite_id=dsvg.identite_id
				and dv.seance_id=esvg.seance_id and dv.manif_id =s.manifestation_id	and dv.commande_id = cmd.commande_id
			LEFT OUTER JOIN depotvente_statut dvs on dvs.entree_id=esvg.entree_id and dvs.identite_id=dsvg.identite_id
				and dvs.seance_id=esvg.seance_id and dvs.manif_id =s.manifestation_id				

			LEFT OUTER join identite identDoss ON identDoss.identite_id = dsvg.identite_id
			LEFT OUTER join gestion_place gp on gp.manif_id = m.manifestation_id and gp.seance_id = s.seance_Id and gp.type_tarif_id = tt.type_tarif_id and gp.categ_id = c.categ_id

			LEFT OUTER JOIN recette rec on rec.dossier_id=e.dossier_id and rec.seance_id=e.seance_id and rec.entree_id = e.entree_id 
			LEFT OUTER JOIN Controle_Acces ca on ca.recette_id=rec.recette_id AND e.dossier_id='  + LTRIM(STR(@DOSSIERID)) + '

			LEFT OUTER JOIN  Produit_PDF_LOG_ENVOI pple on pple.Dossier_id = dsvg.dossier_id  AND pple.Identite_id = dsvg.identite_id AND pple.Entree_id = esvg.entree_id
			WHERE dsvg.dossier_id='  + LTRIM(STR(@DOSSIERID)) + ' AND cmd.commande_id=' + LTRIM(STR(@COMMANDEID))
			+ ' AND type_ligne=''DOS'' 
			
					AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@MANIFID)) + ' esvg2 
			WHERE 
				 esvg.entree_id=esvg2.entree_id	and esvg2.dossier_id=esvg.dossier_id
				  and esvg.seance_id=s.seance_Id  and esvg2.entree_etat <>''F'' /* elimine les entrees en mode Facture */
				 )		 
			 AND (dsvg.dossier_etat <> ''P'' or dsvg.type_operation <> ''EDIT'')  /* elimine les lignes en P et EDIT */
			 and  dsvg.type_operation <> ''DUPLI'' 
			
			'
			
		PRINT @SQL
		EXEC (@SQL)
	END
	
	--print @MANIFID
	--print @COMMANDEID
FETCH  NEXT FROM complex_cursor INTO @SEANCE_ID, @MANIFID, @COMMANDEID,@DOSSIERID,@SEANCE_DATE_DEB;
	END	


	CLOSE complex_cursor;
DEALLOCATE complex_cursor;

	
-- ENTREES :

update #TmpEntree set rank='' where notnumbered = 1

SELECT  * FROM #TmpEntree order by orderid desc



drop table #TmpEntree