
/* getMObentionByGpsIds.sql */

select produit_id /* si au final on propose ce produit + d'autres public "tout public' on exclus ce produits */
into #prodAExclure
from produit where produit_id in ({prodJustifId})

SELECT gestion_place_id 
INTO #mygp
FROM gestion_place gp 
WHERE gestion_place_id in ({gpids})

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

DECLARE @nbrGps int /* nbr de gps */
SELECT @nbrGps = count(*) from #mygp
--select @nbrGps

SELECT 
p.produit_id as productId,
CASE WHEN tp.produit_nom is null then p.produit_nom else tp.produit_nom end as productName,

CONVERT(INTEGER,(montant1 + montant2)*100) as TotalAmount ,CONVERT(INTEGER,montant2 * 100)  as Charge,
 isnull(tva.tva_libelle,'') as tva_libelle , isnull(tva.tva_taux,0) as tva_taux,
 p.pref_affichage,
 pe.produit_id as produitAExclure 
 INTO #myProductsMO
 FROM 
(
	SELECT count(*) as nbrGpReliees, p.produit_id 
	FROM gestion_place_type_envoi gpte 
	inner join #mygp mygp on mygp.gestion_place_id = gpte.gestion_place_id
	inner join produit p on p.produit_id = gpte.produit_id 
	GROUP BY p.produit_id
) s 
INNER JOIN produit p on p.produit_id = s.produit_id
INNER JOIN produit_stock ps on ps.produit_id=p.produit_id 
LEFT JOIN #prodAExclure pe on pe.produit_id = p.produit_id
LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @LgId

LEFT OUTER JOIN tva ON tva.tva_id = p.tva1
WHERE nbrGpReliees = @nbrGps /* les mo communs ont autant de gp reliés que les gps demandés */

DECLARE @countProduitsTTPublic INT
SELECT @countProduitsTTPublic = count(*) FROM #myProductsMO WHERE produitAExclure is null
IF (@countProduitsTTPublic > 0)
BEGIN /* on exclut les produits à justif s'il sont proposés avec d'autres produits */
	delete #myProductsMO where produitAExclure is not null
	/* ex : on ne propose que la produit "envoi pdf sous reserve de justif" => ok, on laisse
	on propose le produit "envoi soumis a justif" + produit "pdf" : on supp le produit "envoi soumis a justif"
	cf ticket CAS-68929-S4S1M9
*/
END

SELECT *
FROM #myProductsMO p

DROP TABLE #myProductsMO
DROP TABLE #prodAExclure