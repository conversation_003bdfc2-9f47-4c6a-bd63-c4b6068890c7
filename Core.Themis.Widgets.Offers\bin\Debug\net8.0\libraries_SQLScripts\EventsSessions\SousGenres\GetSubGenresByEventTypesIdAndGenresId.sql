﻿-- Type de manif + Genre
-- Get sous genres
/*
DECLARE @pEventsTypesId varchar(max) = '0'
DECLARE @pGenresId varchar(max) = '0'
DECLARE @pLangCode varchar(5) = 'fr'
DECLARE @pBuyerProfilId int = 0
DECLARE @pIdentiteId int =0
*/

DECLARE @langueId int = (SELECT langue_id FROM langue where langue_code = @pLangCode)


CREATE TABLE #myoffres
(
offre_id INT,
offre_nom VARCHAR(200)
)
EXEC [Sp_ws_getoffres_totable]
@pIdentiteId,
@pBuyerProfilId



DECLARE @sOffresId NVARCHAR(MAX)

SELECT @sOffresId = ISNULL(
STUFF(
( SELECT ',' + CONVERT(NVARCHAR(20), offre_id)
FROM #myoffres
FOR xml path('')
) , 1 , 1 , '')
,'')


DECLARE @isrevendeur int = 0
SELECT @isrevendeur = count(*) from profil_acheteur pa WHERE pa.id = @pBuyerProfilId AND pa.is_revendeur = 1

DECLARE @addgeneralsrules int = 0 /* 0 = on n'ajoute pas l'offre generale, 1 on ajoute */


declare @sqlColonne nvarchar(500)
set @sqlColonne = 'SELECT @n = count(*) from profil_acheteur pa WHERE pa.id = ' + convert(varchar(10),@pBuyerProfilId) + N' AND pa.add_generals_rules = 1'

begin try
exec sp_executesql @sqlColonne, N'@n int out', @addgeneralsrules out
end try
begin catch
end catch


DECLARE @TempTableManifs TABLE (
	nom varchar(max),
	manifestation_id int,
	ID_genre int, 
	manifestation_groupe_id int)

DECLARE @ManifGenre TABLE (manif_id INT)
SELECT name INTO #tmpGenresIds FROM splitstring (@pGenresId,',') 

IF (SELECT COUNT(*) FROM #tmpGenresIds WHERE name = 0) > 0
BEGIN
	IF (@sOffresId ='') --Sans offre
	BEGIN
		INSERT INTO @TempTableManifs
		SELECT manifs.manifestation_nom as nom, manifs.manifestation_id, manifs.ID_genre, manifs.manifestation_groupe_id
		FROM (
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND gp.isContrainteIdentite = 0
			UNION
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id
			LEFT OUTER JOIN offre_contrainte oc ON oc.offre_id = o.offre_id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			AND oc.contrainte_id IS NULL /* offres adh sans contraintes */
		) manifs
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
		LEFT JOIN manifestation_genre mg ON mg.id = manifs.ID_genre
		LEFT JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
		WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
		AND (mgg.id IN (select * from #tmpGenresIds)
			OR manifs.Id_Genre = 0)		
	END

	IF (@sOffresId <> '') --Avec offre
	BEGIN
		INSERT INTO @TempTableManifs
		SELECT manifs.manifestation_nom as nom, manifs.manifestation_id, manifs.ID_genre, manifs.manifestation_groupe_id
		FROM (
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id and gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id 
			INNER JOIN #myoffres myo ON myo.offre_id =ogp.offre_Id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			UNION
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id
			LEFT OUTER JOIN offre_contrainte oc ON oc.offre_id = o.offre_id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			AND oc.contrainte_id IS NULL /* offres adh sans contraintes */
		) manifs
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
		LEFT JOIN manifestation_genre mg ON mg.id = manifs.ID_genre
		LEFT JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
		WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
		AND (mgg.id IN (select * from #tmpGenresIds)
			OR manifs.Id_Genre = 0)


		IF (@addgeneralsrules =1) -- on ajoute les manifs tout publics
		BEGIN
			INSERT INTO @TempTableManifs
			SELECT manifs.manifestation_nom AS nom, manifs.manifestation_id, manifs.ID_genre, manifs.manifestation_groupe_id
			FROM (
				SELECT DISTINCT m.* FROM manifestation m
				INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
				INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
				WHERE gp.isvalide = 1 
				AND s.seance_cloturer = 'N' 
				AND s.seance_verrouiller = 'N' 
				AND s.supprimer = 'N' 
				AND m.supprimer = 'N'
				AND isContrainteIdentite = 0
			) manifs
			INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
			LEFT JOIN manifestation_genre mg ON mg.id = manifs.ID_genre
			LEFT JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
			WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
			AND (mgg.id IN (select * from #tmpGenresIds)
				OR manifs.Id_Genre = 0)		
		END
	END
END
ELSE
BEGIN
	IF (@sOffresId ='') --Sans offre
	BEGIN
		INSERT INTO @TempTableManifs
		SELECT manifs.manifestation_nom as nom, manifs.manifestation_id, manifs.ID_genre, manifs.manifestation_groupe_id
		FROM (
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND gp.isContrainteIdentite = 0
			UNION
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id
			LEFT OUTER JOIN offre_contrainte oc ON oc.offre_id = o.offre_id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			AND oc.contrainte_id IS NULL /* offres adh sans contraintes */
		) manifs
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
		INNER JOIN manifestation_genre mg ON mg.id = manifs.ID_genre
		INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
		WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
		AND mgg.id IN (select * from #tmpGenresIds)
	END

	IF (@sOffresId <> '') --Avec offre
	BEGIN
		INSERT INTO @TempTableManifs
		SELECT manifs.manifestation_nom as nom, manifs.manifestation_id, manifs.ID_genre, manifs.manifestation_groupe_id
		FROM (
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id and gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id 
			INNER JOIN #myoffres myo ON myo.offre_id =ogp.offre_Id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			UNION
			SELECT DISTINCT m.* FROM manifestation m
			INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
			INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
			INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
			INNER JOIN offre o ON ogp.offre_id = o.offre_id
			LEFT OUTER JOIN offre_contrainte oc ON oc.offre_id = o.offre_id
			WHERE gp.isvalide = 1 
			AND s.seance_cloturer = 'N' 
			AND s.seance_verrouiller = 'N' 
			AND s.supprimer = 'N' 
			AND m.supprimer = 'N'
			AND isContrainteIdentite = 1
			AND oc.contrainte_id IS NULL /* offres adh sans contraintes */
		) manifs
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
		INNER JOIN manifestation_genre mg ON mg.id = manifs.ID_genre
		INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
		WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
		AND mgg.id IN (select * from #tmpGenresIds)

		IF (@addgeneralsrules =1) -- on ajoute les manifs tout publics
		BEGIN
			INSERT INTO @TempTableManifs
			SELECT manifs.manifestation_nom AS nom, manifs.manifestation_id, manifs.ID_genre, manifs.manifestation_groupe_id
			FROM (
				SELECT DISTINCT m.* FROM manifestation m
				INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
				INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id AND gp.seance_id = s.seance_Id
				WHERE gp.isvalide = 1 
				AND s.seance_cloturer = 'N' 
				AND s.seance_verrouiller = 'N' 
				AND s.supprimer = 'N' 
				AND m.supprimer = 'N'
				AND isContrainteIdentite = 0
			) manifs
			INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = manifs.manifestation_groupe_id 
			INNER JOIN manifestation_genre mg ON mg.id = manifs.ID_genre
			INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
			WHERE mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
			AND mgg.id IN (select * from #tmpGenresIds)
		END
	END
END

SELECT DISTINCT mg.id as Id, ISNULL(tmg.nom, mg.nom) AS Name, mgg.id as IdLinked
FROM @TempTableManifs m 
INNER JOIN manifestation_genre mg ON mg.id = m.id_genre
INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
LEFT OUTER JOIN traduction_manifestation_genre tmg ON tmg.id = mg.id AND langue_id = @langueId
		
DROP TABLE #myoffres
DROP TABLE #tmpGenresIds
