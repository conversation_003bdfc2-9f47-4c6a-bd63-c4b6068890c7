DECLARE @groupCarteCadeau int = 9
DECLARE @groupBoutique int = 99
DECLARE @groupSans int = 0
DECLARE @groupCAdh int = 3
DECLARE @groupResto int = 2
DECLARE @groupMO int =6
DECLARE @groupAssurance int = 7
DECLARE @groupReservation int = 12
DECLARE @groupFraisEnvoi int = 1


Declare @TblFunctions table (BlockFunctionId int, FunctionId int, FunctionName varchar(250) )
[FOREACH.INSERTINTO]

/*
INSERT INTO @TblFunctions VALUES (3, 2, 'Products_Family')
INSERT INTO @TblFunctions VALUES (5, 3, 'Products_Subfamily')
INSERT INTO @TblFunctions VALUES (36, 19, 'Select_Products_List_Multiple')
INSERT INTO @TblFunctions VALUES (34, 20, 'Select_Products_Family_List_Multiple')
INSERT INTO @TblFunctions VALUES (35, 21, 'Select_Products_Subfamily_List_Multiple')
INSERT INTO @TblFunctions VALUES (31, 17, 'Select_Front_MsgIntro')
INSERT INTO @TblFunctions VALUES (32, 18, 'Select_Front_MsgIntro')
INSERT INTO @TblFunctions VALUES (39, 16, 'Input_Items_Max_To_Display')
INSERT INTO @TblFunctions VALUES (37, 16, 'Input_Stock_Filling_Min')
INSERT INTO @TblFunctions VALUES (38, 16, 'Input_Stock_Filling_Max')
*/
/*
declare @pBlockEmplacement int = 287
declare @pLangCode varchar(5) = 'fr'
declare @pFiliereId int =  5650004
*/

declare @BlockFunction_ID int 
Declare @SQL varchar(max)


Set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Input_Items_Max_To_Display')
Declare @NbTop int = ISNULL((select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ), 0)  --- Par défaut 8, mais pourra être changé

declare @ProfilAcheteurId int =0 --- Normalement, on ne gère pas le profil d'acheteur, mais à penser. donc 0 par défaut


Set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Input_Stock_Filling_Min')
Declare @FiltreRemplissageMin int = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 

Set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Input_Stock_Filling_Max')
Declare @FiltreRemplissageMax int = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 

--- Attention, chaque filtre est additionnel

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Products_List_Multiple')
declare @FiltreProduit varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )  --'1;2;3;4;7;8;9;10;11;12;13;14' --- Filtre à définir à l'appel du SQL  Select_Events_List_Multiple

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Products_Family_List_Multiple')
Declare @FiltreFamilleProduit varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                         where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2;3;4' --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Groupe_genre = Genre Select_Events_Genre_List_Multiple

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Products_Subfamily_List_Multiple')
Declare @FiltreSousFamilleProduit Varchar(max) = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                         where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2' --- --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Genre = Sous_Genre 

---- 


----- VA 26/09/2023  -- Gestion des stocks. Insertion dans un table temp les stocks des produits selon la filiere . Table réutilisée dans la requête finale pour les produits boutique.
declare @TmpStock table ( produit_id int, Stock int )
DECLARE @colStock int = 0
declare @SqlStock varchar(max)

SELECT @colStock = replace(droit_boutique_code,'DEPOT_FILIERES_','') FROM Produit_Droit_Boutique WHERE droit_boutique_code LIKE
	'DEPOT_FILIERES_%' and droit_boutique_champ like '%' + convert(varchar(100),@pFiliereId) + '%'
if @colStock > 0
	begin
		SET @SqlStock = 'Select produit_id , restant_' +  convert(varchar(10), @colStock) + ' from produit_stock '
		insert into @TmpStock exec(@SqlStock)
	end
------ fin VA 26/09/2023 


--select * from @TmpStock


Declare @TblDispoProduits table (Produit_Nom varchar(250)
                                                      , Produit_id int 
                                                      , produit_pref_affichage int
                                                      , Produit_Famille_id int
                                                      , Produit_Famille_Nom varchar(250)
                                                      , Produit_Sous_Famille_id int 
                                                      , Produit_Sous_Famille_nom varchar(250)
                                                      , StockDispo  int 
                                                      , StockTotal int
                                                      , GroupeId int
                                                      , TauxDisponible decimal(18,2))


INSERT INTO @TblDispoProduits 
select p.produit_nom, p.produit_id, p.pref_affichage, plsf.Produit_Famille_ID, pf.Produit_Famille_Nom,
plsf.Produit_Sous_Famille_ID, psf.Produit_Sous_Famille_Nom,
ps.restant as StockDispo,
ps.jauge as StockTotal, p.groupe_id,
--round((  (cast(ps.jauge as decimal(18,2)) - cast(ps.restant as decimal(18,2) ) )   / ps.restant *100.0),2) as Taux_Remplissage

 CASE WHEN ps.jauge > 0
 THEN
	 (cast(ps.restant as decimal(18,2)) * 100  / ps.jauge) 

 END as TauxDisponible
 
FROM produit p 
INNER JOIN produit_stock ps ON ps.produit_id=p.produit_id
INNER JOIN produit_internet p_i ON p_i.produit_id = p.produit_id
LEFT OUTER JOIN Produit_Lien_Sous_Famille plsf ON p.produit_id = plsf.Produit_id
LEFT OUTER JOIN Produit_Famille pf ON pf.Produit_Famille_ID = plsf.Produit_Famille_ID
LEFT OUTER JOIN Produit_Sous_Famille psf ON psf.Produit_Sous_Famille_ID = plsf.Produit_Sous_Famille_ID
LEFT OUTER join @TmpStock stk on stk.produit_id = p.produit_id -- VA 26/09/2023
where p.internet = 1 AND
 p.groupe_id NOT IN( @groupMO,  @groupAssurance, @groupReservation, @groupFraisEnvoi)
 AND p.produit_anu <>  @groupBoutique
 AND p_i.acces_autonome = 1 AND ps.manifestation_id=0 AND ps.seance_id =0
AND ps.jauge > 0

 -----------------------------
 --select @FiltreFamilleProduit as FiltreFamilleProduit
 --select @FiltreSousFamilleProduit as FiltreSousFamilleProduit
 --select @FiltreProduit as FiltreProduit

 
--remplace le -1 par 0 
select @FiltreFamilleProduit = replace(@FiltreFamilleProduit, '-1','0' ) 

 DECLARE @TmpFiltreFamille TABLE(Val int) 
 IF @FiltreFamilleProduit <> ''
BEGIN 

	declare @countNegativeValue int = (select count(*) from splitstring(@FiltreFamilleProduit, ';') where  sign(Name) = -1 )
	declare @countPositiveValue int = (select count(*) from splitstring(@FiltreFamilleProduit, ';') where  sign(Name) = 1 )
             
	if @countNegativeValue > 0 and @countPositiveValue > 0 -- si on a des valeurs négative et positive ==> familles + produits sans famille
	BEGIN
		--SET @SQL = 'SELECT produit_id FROM Produit_Lien_Sous_Famille plsf where Produit_Lien_Famille_ID in  (SELECT replace(name, ''-'','''' ) FROM splitstring('''+@FiltreFamilleProduit+''', '';'') where  sign(Name) = 1)'
		--SET @SQL += 'UNION '
		--SET @SQL += 'SELECT produit_id FROM produit where groupe_id in(select replace(name, ''-'','''' ) FROM splitstring('''+@FiltreFamilleProduit+''', '';'') WHERE  sign(Name) = -1 )'
		
		SET @SQL = 'select Produit_id from Produit_Famille pf inner join Produit_Lien_Sous_Famille plsf ON plsf.Produit_Famille_ID = pf.Produit_Famille_ID where pf.Produit_Famille_ID in(select name FROM splitstring('''+@FiltreFamilleProduit+''', '';'') )'
		SET @SQL += 'UNION '
		SET @SQL += 'SELECT produit_id FROM produit where groupe_id in(select replace(name, ''-'','''' ) FROM splitstring('''+@FiltreFamilleProduit+''', '';'') WHERE  sign(Name) = -1 )'

		
		print @SQL
	END
	ELSE IF @countNegativeValue = 0 and @countPositiveValue > 0 -- si on a des valeurs toutes positives ==> familles
	BEGIN
		SET @SQL = 'SELECT produit_id FROM Produit_Lien_Sous_Famille where Produit_Famille_ID in (' + replace(@FiltreFamilleProduit,';',',') +  ') '
		print @SQL
	END
	ELSE IF @countNegativeValue > 0 and @countPositiveValue = 0 -- si on a des valeurs toutes négatives ==> produits sans famille
	BEGIN
		SET @SQL = 'SELECT produit_id FROM produit where groupe_id in( select replace(name, ''-'','''' ) FROM splitstring('''+@FiltreFamilleProduit+''', '';'') ) AND internet = 1 ' 
		print @SQL
	END

	INSERT INTO @TmpFiltreFamille EXEC (@SQL) 
	DELETE @TblDispoProduits WHERE Produit_id not in (SELECT val FROM @TmpFiltreFamille)
	DELETE @TmpFiltreFamille

END 


 DECLARE @TmpFiltreSousFamille TABLE (Val int) 
 IF @FiltreSousFamilleProduit <> ''
BEGIN 
		Set @SQL = 'select produit_id FROM Produit_Lien_Sous_Famille where produit_sous_famille_id in (' + replace(@FiltreSousFamilleProduit,';',',') +  ') '
		INSERT INTO @TmpFiltreSousFamille exec (@SQL) 

		DELETE @TblDispoProduits WHERE Produit_id not in (SELECT val FROM @TmpFiltreSousFamille)
		DELETE @TmpFiltreSousFamille
END 


 DECLARE @TmpFiltreProduit TABLE (Val int) 
 IF @FiltreProduit <> ''
BEGIN 
		 Set @SQL = 'Select produit_id  from produit  where produit_id in (' + replace(@FiltreProduit,';',',') +  ') ' 
			
		INSERT INTO @TmpFiltreProduit exec (@SQL) 

		DELETE @TblDispoProduits WHERE Produit_id not in (SELECT val FROM @TmpFiltreProduit)
		DELETE @TmpFiltreProduit

END 
 -----------------------------

-- select * from @TblDispoProduits


SELECT TOP (@NbTop) Produit_id as ProductFeatureId, Produit_Nom as ProductFeatureName, produit_pref_affichage as ProductFeaturePrefAffichage,
produit_Famille_id as ProductFeatureFamilleId, Produit_Famille_Nom as ProductFeatureFamilleName,
Produit_Sous_Famille_id as ProductFeatureSousFamilleId, Produit_Sous_Famille_nom as ProductFeatureSousFamilleName, StockDispo as ProductFeatureStockDispo,
StockTotal as ProductFeatureStockTotal
FROM @TblDispoProduits dispProduits
WHERE TauxDisponible BETWEEN @FiltreRemplissageMin and @FiltreRemplissageMax

ORDER BY newid()
