﻿/*
declare @pentree_id int	  = 0
declare @pseance_id int	  = 0
declare @pconsumer_id int = 0
declare @pdossier_id int  = 0
declare @pidentite_id int = 0
*/




DECLARE @pname_surname varchar(400)
DECLARE @NewTarifCommentaireId int 
DECLARE @NbResult int

SELECT @NbResult = count(*) FROM Entree_Complement WHERE entree_id = @pentree_id and Identite_id = @pidentite_id  and seance_id = @pseance_id and dossier_id = @pdossier_id

IF @NbResult = 0
BEGIN

	SELECT @pname_surname = ltrim(rtrim(identite_nom + ' ' + identite_prenom)) FROM identite WHERE identite_id =  @pconsumer_id
	IF @pconsommateur_id_IS_consumer = 1
	BEGIN
		SELECT @pname_surname = ltrim(rtrim(name + ' ' + surname)) FROM consumers WHERE consumer_id =  @pconsumer_id
	END

	INSERT INTO Tarif_Commentaire (tarif_commentaire_libelle, tarif_commentaire_code, type_tarif_id, ordre, masquer) values (@pname_surname, '', 0, 1, 0)

	SELECT @NewTarifCommentaireId = SCOPE_IDENTITY()

	INSERT INTO Entree_Complement (entree_id, seance_id, consommateur_ID,Beneficiaire_Identite_id, Tarif_Commentaire_ID, Mode_Obtention_ID, Dossier_id, identite_id, consommateur_id_IS_consumer)
	VALUES (@pentree_id, @pseance_id, @pconsumer_id, @pbeneficiaire_identite_id, @NewTarifCommentaireId, 0, @pdossier_id, @pidentite_id, @pconsommateur_id_IS_consumer)

END