﻿/*
declare @pManifestationGenreId varchar(max) ='1' 
*/

IF @pManifestationGenreId <> ''
BEGIN


	SELECT name into #tmpSousGenreToSearch FROM splitstring (@pManifestationGenreId,',') c
	
	DECLARE @cntGenreAucunGroupe int =  (select COUNT(*) from #tmpSousGenreToSearch where Name = 0)

	IF @cntGenreAucunGroupe > 0
	BEGIN
		SELECT DISTINCT mg.* FROM manifestation_genre mg
		INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id 
		INNER JOIN manifestation m on mg.id = m.ID_genre 
		INNER JOIN gestion_place gp on gp.manif_id = m.manifestation_id
		WHERE gp.isvalide = 1 and mgg.id in (SELECT * FROM #tmpSousGenreToSearch)
	
		UNION

		SELECT DISTINCT mg.* 
		FROM manifestation_genre mg
		WHERE groupe_id = 0
	END
	ELSE
	BEGIN
		SELECT DISTINCT mg.* FROM manifestation_genre mg
		INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id 
		INNER JOIN manifestation m on mg.id = m.ID_genre 
		INNER JOIN gestion_place gp on gp.manif_id = m.manifestation_id
		WHERE gp.isvalide = 1 and mgg.id in (SELECT * FROM #tmpSousGenreToSearch)
	
	END


	DROP TABLE #tmpSousGenreToSearch
END
ELSE
BEGIN
	
	SELECT DISTINCT mg.* from manifestation_genre mg
	INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
	INNER JOIN manifestation m on mg.id = m.ID_genre 
	INNER JOIN gestion_place gp on gp.manif_id = m.manifestation_id
	WHERE gp.isvalide = 1  
	UNION
	
	SELECT DISTINCT mg.* 
	FROM manifestation_genre mg
	WHERE groupe_id = 0

END
