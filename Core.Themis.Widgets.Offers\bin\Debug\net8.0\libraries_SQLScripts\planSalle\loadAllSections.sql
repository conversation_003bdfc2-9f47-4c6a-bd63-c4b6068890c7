
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT s.lieu_id, s.section_id,
	case when t.section_nom is null then s.section_nom else t.section_nom end as section_nom,
	s.section_code, s.pref_affichage, s.section_couleur_id 
FROM section s
LEFT OUTER JOIN traduction_section t on t.section_id = s.section_id and t.langue_id = @LgId
order by s.pref_affichage, s.lieu_id
