
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT c.lieu_id, c.categ_id, 
	case when tc.categ_nom is null then c.categ_nom else tc.categ_nom end as categ_nom,
	c.categ_code, c.pref_affichage, c.categ_couleur_id 
FROM categorie c
LEFT OUTER JOIN traduction_categorie tc on tc.categ_id = c.categ_id and tc.langue_id = @LgId
order by c.pref_affichage

