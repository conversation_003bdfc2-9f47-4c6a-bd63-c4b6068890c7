 /* 
 declare @peventid int = 23
 declare @pseance_id int = 23
 declare @pseatId int = 23
 declare @pdossierId int = 23
 */
 
 SELECT numbillet, externe, motif, manifestation_id, entree_id, recette_id, seance_id, type_operation, date_operation
 FROM (
	SELECT top 1 numbillet, externe, motif, manifestation_id, entree_id, recette_id,type_operation, seance_id, date_operation
	FROM recette WHERE manifestation_id=@pEventId and dossier_id = @pdossierId and seance_id=@psessionId and  entree_id=@pseatId ORDER BY recette_id desc
) s 
WHERE type_operation in ('E','D') /* si dernier etat <> 'E' ou 'D' => retourne vide */