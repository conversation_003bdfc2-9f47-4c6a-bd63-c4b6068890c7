﻿--DECLARE @pLangCode VARCHAR(5) = 'fr'

DECLARE @langueId INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

SELECT DISTINCT 
pf.Produit_Famille_ID AS Id,
IIF(t.Traduction = '' OR t.Traduction IS NULL, pf.Produit_Famille_Nom , t.traduction Collate database_default) AS Name
FROM Produit_Famille pf
LEFT JOIN traduction_langue t on t.traduction_id = pf.TraductionNom_ID AND t.langue_id = 1
WHERE pf.Masquer = 0
