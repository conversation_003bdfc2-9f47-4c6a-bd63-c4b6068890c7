

--EXEC [PanierTransformatorAdhesion] @panier_entree = @thispanierentree, @panier_produit = @thispanierproduit, @identite_id=@pidentiteId

declare @identite_id int = @pidentiteId

DECLARE @nbrplaces int, @tarif_id int, @nb_max int, @nb_min int,
	@manif_id int, @seance_id int
--CREATE TABLE #HistoriqueUser (nbr int, session_id int,manif_id int,  type_tarif_id int );
--CREATE TABLE #HistoriqueUserGroupe (nbr int, type_tarif_id int );

create table #CartesToAdd(nbr int, produit_id int, catalog_id int, consumer_id int)

declare @panierWork tpanierentree2
insert into @panierWork select * from @thispanierentree

UPDATE @panierWork set consumer_id=@identite_id where consumer_id = 0

-- reinitialise le panier dans la table temp @panierwork
UPDATE @panierWork set type_tarif_id=gestion_place.type_tarif_id
from @panierWork p
inner join gestion_place 
on (gestion_place.gestion_place_id=p.gestion_place_id)

select *, 0 as nbrHistoCeTarif, 
0 as nbrHistoCeTarifCetteManif, 
0 as nbrHistoCeTarifCetteSeance, 
0 as TT, '_____________________' as regleAppliquee INTO #panierWorkWithColNewTarif from @panierWork

declare @thisConsumerId int
DECLARE cur_consumer_id CURSOR SCROLL FOR /* *********** curseur sur les consumers du panier */
	select 
	distinct(consumer_id)
	from @panierWork p 

OPEN cur_consumer_id
FETCH NEXT FROM cur_consumer_id INTO @thisConsumerId

WHILE @@FETCH_STATUS = 0
BEGIN


--select  '-----', tt.type_tarif_id, tt.type_tarif_nom, * from offre_gestion_place ogp 
--inner join adhesion_catalog_offresliees ao on ao.offre_id  = ogp.offre_id
--inner join @panierWork p on ogp.gestion_place_id = p.gestion_place_id
--inner join type_tarif tt on tt.type_tarif_id = p.type_tarif_id 
--where consumer_id = @thisConsumerId

			declare @thisManifId int, @thisTarifId int
			DECLARE cur_manif_parManif CURSOR SCROLL FOR /* *********** curseur */
				select 
					tt.type_tarif_id, p.manif_id from offre_gestion_place ogp 
					inner join adhesion_catalog_offresliees ao on ao.offre_id  = ogp.offre_id
					inner join Adhesion_Catalog_Propriete catprop on catprop.Adhesion_Catalog_ID = ao.adhesion_catalog_id
					inner join #panierWorkWithColNewTarif p on ogp.gestion_place_id = p.gestion_place_id
					inner join gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id and gp.type_tarif_id = catprop.Propriete_Valeur_Int1
					inner join type_tarif tt on tt.type_tarif_id = gp.type_tarif_id 
					where consumer_id = @thisConsumerId
					and catprop.Propriete_Code= 'TARIF_MAITRE' 
			

			OPEN cur_manif_parManif
			FETCH NEXT FROM cur_manif_parManif INTO @thisTarifId, @thisManifId

			WHILE @@FETCH_STATUS = 0
			BEGIN

				--select @thisConsumerId, @thisTarifId, @thisManifId
				declare @nDejaPris BIGINT
				declare @sqlH nvarchar(max)
				DECLARE @params NVARCHAR(255) = '@dejaP BIGINT OUTPUT'
				--set @sqlH = 'SELECT @dejaP= count(*)  FROM entree_' + convert(varchar,@thisManifId);
				set @sqlH = 'SELECT @dejaP= count(*)  FROM entree_' + convert(varchar,@thisManifId) 

				--print @sqlH

				--exec sp_executesql @sqlH, N'@nDejaPris int output', @nDejaPris output;
				EXEC sp_executeSQL @sqlH, @params, @dejaP = @nDejaPris OUTPUT;
				--exec @nDejaPris = sp_executesql @sqlH;
				
				
				----------------- 
				set @nDejaPris = 0

				--select '@nDejaPris=', @nDejaPris
				update #panierWorkWithColNewTarif set nbrHistoCeTarifCetteManif = @nDejaPris where type_tarif_id = @thisTarifId and manif_id = @thisManifId and consumer_id = @thisConsumerId


				FETCH NEXT FROM cur_manif_parManif INTO @thisTarifId, @thisManifId
			END

			CLOSE cur_manif_parManif
			DEALLOCATE cur_manif_parManif

			SELECT MAX(occurenceEtHistoCeTarifManif) as nbrTarif, type_tarif_id, type_tarif_nom, consumer_id, adhesion_catalog_id 
			INTO #nbrTarifByCatalogAdhesion
			FROM (
				SELECT  
	
				nbrHistoCeTarifCetteManif + ROW_NUMBER ( ) over (PARTITION BY  p.type_tarif_id, p.manif_id ORDER BY p.manif_id, p.type_tarif_id  asc)
				 as occurenceEtHistoCeTarifManif
				 ,	
				tt.type_tarif_id, tt.type_tarif_nom, 
				p.entree_id, p.manif_id, p.session_id, p.categorie_id, p.gestion_place_id, ao.adhesion_catalog_id, ao.offre_id, p.consumer_id

				FROM offre_gestion_place ogp 
				inner join adhesion_catalog_offresliees ao on ao.offre_id  = ogp.offre_id
				inner join Adhesion_Catalog_Propriete catprop on catprop.Adhesion_Catalog_ID = ao.adhesion_catalog_id
				inner join #panierWorkWithColNewTarif p on ogp.gestion_place_id = p.gestion_place_id
				inner join gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id and gp.type_tarif_id = catprop.Propriete_Valeur_Int1
				inner join type_tarif tt on tt.type_tarif_id = gp.type_tarif_id 
				WHERE consumer_id = @thisConsumerId
				and catprop.Propriete_Code= 'TARIF_MAITRE' 
				) ss 
				GROUP BY type_tarif_id, type_tarif_nom, consumer_id, adhesion_catalog_id 
			

			--create table #CartesToAdd(nbr int, produit_id int, catalog_id int, consumer_id int)
			insert into #CartesToAdd				
		SELECT nbrTarif -		
			(select COUNT(*) from Adhesion_Adherent_Catalog aac
			inner join Adhesion_Adherent aa on aa.Adhesion_Adherent_ID = aac.Adhesion_Adherent_ID 
			where aac.Adhesion_Catalog_ID = cat.Adhesion_Catalog_ID and Adhesion_DateFin > GETDATE() -- and Adhesion_DateDeb< GETDATE()
			and actif=1
			and aa.identite_id = @thisConsumerId
		) as nbrAdhesionToAdd,
		 cat.Produit_ID, cat.Adhesion_Catalog_ID, @thisConsumerId from #nbrTarifByCatalogAdhesion p
		 inner join Adhesion_Catalog cat on p.adhesion_catalog_id = cat.Adhesion_Catalog_ID
		 inner join produit pr on pr.produit_id = cat.Produit_ID
		 
	drop table #nbrTarifByCatalogAdhesion		 
		 
		 	
	FETCH NEXT FROM cur_consumer_id INTO  @thisConsumerId
END

CLOSE cur_consumer_id
DEALLOCATE cur_consumer_id



--select * from #CartesToAdd
--select * from #panierWorkWithColNewTarif
 
select  'PROD' as typeG, cartesToAdd.nbr, p.produit_id as id, 
produit_nom as nom,  
convert(int, vts_grille1*100) as vts_grille1, 
convert(int, vts_grille2*100) as vts_grille2 
,consumer_id, catalog_id
into #tempP 
from produit p
inner join #CartesToAdd cartesToAdd on cartesToAdd.produit_id = p.produit_id

--select * from #CartesToAdd
select * from #tempP
group by typeG, nbr, id, nom , vts_grille1, vts_grille2, consumer_id, catalog_id

DROP TABLE #tempP
DROP TABLE #CartesToAdd
DROP TABLE #panierWorkWithColNewTarif

