﻿/*
DECLARE @pLangCode VARCHAR(2) = 'DE'
DECLARE @pSessionsId VARCHAR(MAX) = '2536,22'
*/

DECLARE @langue_id INT = ISNULL((SELECT langue_id FROM langue WHERE langue_code = @pLangCode), 1)



SELECT c.categ_id, s.lieu_id,  ISNULL(tc.categ_nom, c.categ_nom)  AS categ_nom FROM categorie C 
INNER JOIN seance s ON s.lieu_id = c.lieu_id
LEFT OUTER JOIN traduction_categorie tc ON tc.categ_id = c.categ_id AND tc.langue_id = @langue_id
WHERE seance_id IN (SELECT name FROM splitstring(@pSessionsId ,','))
ORDER BY c.pref_affichage