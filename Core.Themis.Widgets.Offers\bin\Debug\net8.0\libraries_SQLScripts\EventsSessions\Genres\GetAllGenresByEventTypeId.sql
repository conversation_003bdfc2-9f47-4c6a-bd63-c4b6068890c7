-- Type de manif

-- Get genre
/*
DECLARE @pEventsTypesId VARCHAR(MAX) ='0'
DECLARE @pLangCode VARCHAR(5) = 'fr'
*/

DECLARE @language_id INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

CREATE TABLE #myGenres
(
	Id INT,
	Name VARCHAR(200)
)

INSERT INTO #myGenres
SELECT DISTINCT mgg.id as Id, ISNULL(tmgg.nom, mgg.nom) AS Name 
FROM manifestation m
INNER JOIN manifestation_genre mg ON mg.id = m.ID_genre
INNER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
LEFT OUTER JOIN traduction_manifestation_groupe_genre tmgg ON tmgg.id = mgg.id AND langue_id = @language_id
INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id 
WHERE mgr.supprimer = 'N'
AND mgr.type_evenement IN (SELECT name FROM splitstring(@pEventsTypesId,','))
AND GETDATE() < ANY (SELECT s.seance_date_deb FROM seance s WHERE s.manifestation_id = m.manifestation_id)

IF (0 = ANY (SELECT ID_genre FROM manifestation))
BEGIN
	INSERT INTO #myGenres VALUES (0, 'Autres')
END

SELECT * FROM #myGenres

DROP TABLE #myGenres
