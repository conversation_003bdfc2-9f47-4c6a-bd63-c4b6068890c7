
declare @newdossierC varchar(250) = @pdossierC /* 50c max */
declare @olddossierC varchar(250)  /* 50c max */

DECLARE @colLength int 
SELECT @colLength = character_maximum_length    
  FROM information_schema.columns WHERE table_name = 'dossier_[eventID]' and column_name = 'dossier_c'

SELECT @olddossierC = rtrim(ltrim(dossier_c)) from dossier_[eventID] where dossier_id = @pdossierId

IF (@olddossierC is null or @olddossierC ='')
	SET @newdossierC = @pdossierC
ELSE
	SET @newdossierC = @olddossierC + '#' + @pdossierC

IF (len(@newdossierC) > @colLength)
	SET  @newdossierC = SUBSTRING(@newdossierC, 1, @colLength)


update dossier_[eventID] set dossier_c = @newdossierC WHERE dossier_id = @pdossierId