
/*
DECLARE @pSponsorReference varchar(max) = 'F29C1FEDF749E26E9A24950CDD5A13DA4C04D3A19C1820EEEB307307490D0E1D'
DECLARE @pEventId int = 1
*/


declare @existTable  int = (SELECT  count(*) FROM sys.tables WHERE name ='sponsor_panier_entrees')
if(@existTable > 0)
BEGIN

    select * from sponsor_panier_entrees  spe
    inner join panier_entree pe on spe.panier_entree_id = pe.panier_entree_id
    inner join panier p on p.panier_id = pe.panier_id
    where  p.etat in ('C', 'P') and spe.reference_sponsor = @pSponsorReference and  manif_id = @pEventId

END