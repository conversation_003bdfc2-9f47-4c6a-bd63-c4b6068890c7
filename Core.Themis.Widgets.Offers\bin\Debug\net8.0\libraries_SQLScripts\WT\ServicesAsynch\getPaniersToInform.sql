﻿
/*  ** getPaniersToInform */
--	declare @delayTo int = 10
--	declare @delaySince int = 6000000


DECLARE @NewVersion int = 0

IF EXISTS(SELECT 1 FROM sys.columns 
          WHERE Name = N'action_id'
          AND Object_ID = Object_ID(N'logs_etapes_informEmail'))
	and EXISTS (SELECT 1 
           FROM INFORMATION_SCHEMA.TABLES 
           WHERE TABLE_TYPE='BASE TABLE' 
           AND TABLE_NAME='panier_action_payment')
BEGIN
	SELECT @NewVersion= COUNT(* ) fROM panier_action_payment
END

IF (@NewVersion>0)
BEGIN

	SELECT  p.panier_id, structure_id, act.action_id, act.typePaiement as typeActionPaiement,  act.etat, p.date_operation, p.date_paiement,
	identite_id, web_user_id, p.etat, commande_id, p.transaction_id, p.certificate, p.card_number, p.card_type, email
	FROM panier p
	inner join panier_action_payment act on act.panier_id= p.panier_id
	 WHERE p.etat in ('P') and act.etat='P'
	AND act.action_id NOT IN (select action_id FROM logs_etapes_informEmail lec
		INNER JOIN logs_etapes_creationCmds_reference lecr on lec.etape_id = lecr.etape_id and code = 'EMAILINFORM'
		where lec.panier_id = p.panier_id
	)
	AND act.date_paiement<dateadd(second, -@delayTo,getdate()) 
	AND act.date_paiement>dateadd(second, -@delaySince,getdate())
	AND structure_id IN ([structuresId])
	ORDER BY p.panier_id desc

END
ELSE
BEGIN /* pas d'action dans les logs ou n'est pas renseigné : à l'ancienne */
	SELECT  p.panier_id, structure_id, 0 as action_id, '' as typeActionPaiement,   p.etat, p.date_operation, p.date_paiement,
	identite_id, web_user_id, p.etat, commande_id, transaction_id, certificate, card_number, card_type, email
	FROM panier p
	 WHERE p.etat in ('P')
	AND panier_id NOT IN (select panier_id FROM logs_etapes_informEmail lec
	INNER JOIN logs_etapes_creationCmds_reference lecr on lec.etape_id = lecr.etape_id and code = 'EMAILINFORM'
	)
	AND date_paiement<dateadd(second, -@delayTo,getdate()) 
	AND date_paiement>dateadd(second, -@delaySince,getdate())
	AND structure_id IN ([structuresId])
	ORDER BY p.panier_id desc
END