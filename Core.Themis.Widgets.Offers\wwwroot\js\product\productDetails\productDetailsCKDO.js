$(document).ready(function () {
    initChangeModalAndCollapse()
    
    // Initialize gift card functionality
    initGiftCardControls()
    
    // Bind click event for add to basket button
    $('#addGiftCardToBasket').off('click').on('click', function () {
        console.log('addGiftCardToBasket')
        
        if (!$(this).prop("disabled")) {
            loadingButtonBootstrapOn($(this))
            addGiftCardToBasket()
        }
    })

    formatDevise()
    sendIframeSize()
});

function initGiftCardControls() {
    var productDetail = $('#bel_productdetail')
    var isVariableAmount = productDetail.attr('data-amountisvariable') === 'true'
    
    if (isVariableAmount) {
        // Handle variable amount gift cards
        $('#giftCardAmount').off('input').on('input', function () {
            updateGiftCardTotal()
        })
        
        $('#giftCardQuantity').off('input').on('input', function () {
            updateGiftCardTotal()
        })
        
        // Initialize total
        updateGiftCardTotal()
    } else {
        // Handle fixed amount gift cards
        $('#giftCardQuantity').off('input').on('input', function () {
            updateFixedGiftCardTotal()
        })
        
        // Initialize total
        updateFixedGiftCardTotal()
    }
}

function updateGiftCardTotal() {
    var amount = parseFloat($('#giftCardAmount').val()) || 0
    var quantity = parseInt($('#giftCardQuantity').val()) || 1
    var totalCents = Math.round(amount * quantity * 100)
    
    $('#addGiftCardToBasket .totalAmount').html(SetDeviseCode(totalCents))
    $('#addGiftCardToBasket .totalAmount').attr('data-pricetoformat', totalCents)
    
    // Enable/disable button based on valid amount
    var productDetail = $('#bel_productdetail')
    var minAmount = parseFloat(productDetail.attr('data-minamount')) / 100
    var maxAmount = parseFloat(productDetail.attr('data-maxamount')) / 100
    
    if (amount >= minAmount && amount <= maxAmount && quantity > 0) {
        $('#addGiftCardToBasket').prop('disabled', false)
    } else {
        $('#addGiftCardToBasket').prop('disabled', true)
    }
}

function updateFixedGiftCardTotal() {
    var productDetail = $('#bel_productdetail')
    var unitAmount = parseInt(productDetail.attr('data-amount'))
    var quantity = parseInt($('#giftCardQuantity').val()) || 1
    var totalCents = unitAmount * quantity
    
    $('#addGiftCardToBasket .totalAmount').html(SetDeviseCode(totalCents))
    $('#addGiftCardToBasket .totalAmount').attr('data-pricetoformat', totalCents)
    
    // Enable/disable button based on valid quantity
    var minQuantity = parseInt(productDetail.attr('data-nbmin'))
    var maxQuantity = parseInt(productDetail.attr('data-nbmax'))
    
    if (quantity >= minQuantity && quantity <= maxQuantity) {
        $('#addGiftCardToBasket').prop('disabled', false)
    } else {
        $('#addGiftCardToBasket').prop('disabled', true)
    }
}

function addGiftCardToBasket() {
    var productDetail = $('#bel_productdetail')
    var productId = parseInt(productDetail.attr('data-productid'))
    var isVariableAmount = productDetail.attr('data-amountisvariable') === 'true'
    
    var giftCardData = []
    
    if (isVariableAmount) {
        var amount = parseFloat($('#giftCardAmount').val()) || 0
        var quantity = parseInt($('#giftCardQuantity').val()) || 1
        var amountCents = Math.round(amount * 100)
        
        // For variable amount, we add each card individually
        for (var i = 0; i < quantity; i++) {
            giftCardData.push({
                ProductId: productId,
                Count: 1,
                Amount: amountCents,
                ConsumerId: 0 // Default consumer
            })
        }
    } else {
        var quantity = parseInt($('#giftCardQuantity').val()) || 1
        
        giftCardData.push({
            ProductId: productId,
            Count: quantity,
            Amount: 0, // Fixed amount, will be handled by server
            ConsumerId: 0 // Default consumer
        })
    }
    
    console.log('Gift card data:', giftCardData)
    
    $.ajax({
        type: "POST",
        url: widgetOfferUrl + structureId + "/Product/AddBasketAjax/" + identityId + "/" + webUserId + "/" + buyerProfilId + "/" + langCode,
        data: {
            listProducts: giftCardData,
            token: partnerToken
        },
        success: function (data) {
            console.log('Gift card added successfully:', data)
            
            $('#modalAskContinueShoppingProduct').modal('show')
            
            $('#BtnContinueShoppingProducts').off('click').on('click', function () {
                var msg = {
                    "action": "continueShoppingProduct",
                }
                window.parent.postMessage(msg, '*')
            })
            
            $('#BtnContinueShoppingEvents').off('click').on('click', function () {
                var msg = {
                    "action": "continueShoppingEvents",
                }
                window.parent.postMessage(msg, '*')
            })
            
            $('#BtnGoToBasket').off('click').on('click', function () {
                var msg = {
                    "action": "addBasket",
                    "basketId": data.basketId,
                    "hash": data.hash
                }
                window.parent.postMessage(msg, '*')
            })
        },
        error: function (xhr, status, error) {
            console.log("Product/AddBasketAjax -> Error")
            console.log("Status:", status)
            console.log("Error:", error)
            console.log("Response:", xhr.responseText)
            
            // Show error message to user
            alert("Erreur lors de l'ajout de la carte cadeau au panier. Veuillez réessayer.")
        },
        complete: function () {
            loadingButtonBootstrapOff($("#addGiftCardToBasket"))
            sendIframeSize()
        }
    });
}
