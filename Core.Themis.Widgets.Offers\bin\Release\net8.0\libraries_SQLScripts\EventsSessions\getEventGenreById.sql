/*
declare @pLangCode varchar(50) = 'fr'
declare @pManifestationGroupeGenreId int = 2 --Genre 
*/
IF OBJECT_ID('dbo.traduction_manifestation_groupe_genre') IS  NULL 
BEGIN
    
   
    select mgg.*
    from manifestation_groupe_genre mgg
    left outer join manifestation_genre mg on mgg.id = mg.groupe_id
    where mg.groupe_id = @pManifestationGroupeGenreId
    ORDER by mg.code
END


IF OBJECT_ID('dbo.traduction_manifestation_groupe_genre') IS  NOT NULL 
BEGIN

    DECLARE @langue_id int = (select langue_id from langue where langue_code = @pLangCode)

    
     select mgg.*, trad_manif_groupe_genre.*
    from manifestation_groupe_genre mgg
    left outer join manifestation_genre mg on mgg.id = mg.groupe_id
    LEFT JOIN traduction_manifestation_groupe_genre trad_manif_groupe_genre on mg.groupe_id = trad_manif_groupe_genre.id and trad_manif_groupe_genre.langue_id = @langue_id
    where mgg.id = @pManifestationGroupeGenreId


END


